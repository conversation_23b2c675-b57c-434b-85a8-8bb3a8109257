import 'package:dartz/dartz.dart';
import '../error/failures.dart';

abstract class Usecase<Type, Params> {
  Future<Type> call({Params? params});
}

abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call({required Params params});
}

class NoParams {
  const NoParams();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoParams;
  }

  @override
  int get hashCode => 0;
}
