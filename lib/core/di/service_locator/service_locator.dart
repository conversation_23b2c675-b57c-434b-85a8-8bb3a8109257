// lib/core/di/service_locator.dart
import 'package:escooter/core/configs/app_config.dart';
import 'package:escooter/core/di/service_locator/service_locator.config.dart';
import 'package:escooter/features/auth/data/sources/auth_service.dart';
import 'package:escooter/features/auth/data/sources/mock_auth_service.dart';
import 'package:escooter/features/nfc/data/services/nfc_service.dart';
import 'package:escooter/features/nfc/data/repository/nfc_repository_impl.dart';
import 'package:escooter/features/nfc/domain/repository/nfc_repository.dart';
import 'package:escooter/features/booking/data/services/booking_service.dart';
import 'package:escooter/features/booking/data/repository/booking_repository_impl.dart';
import 'package:escooter/features/booking/domain/repository/booking_repository.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:injectable/injectable.dart';

final getIt = GetIt.instance;

@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: true,
)
Future<void> configureDependencies() async {
  await dotenv.load(fileName: ".env");

  getIt.init();

  // Configure authentication service based on USE_MOCK_AUTH flag
  if (AppConfig.USE_MOCK_AUTH) {
    // Unregister the real service and register mock service instead
    if (getIt.isRegistered<AuthApiService>(instanceName: 'authApiService')) {
      getIt.unregister<AuthApiService>(instanceName: 'authApiService');
    }
    final mockService = MockAuthApiService();
    final mockWrapper = MockAuthServiceWrapper(mockService);
    getIt.registerFactory<AuthApiService>(() => mockWrapper, instanceName: 'authApiService');
    print('🔧 Mock Authentication Service registered for testing');
  }

  assert(getIt.isRegistered<http.Client>(instanceName: 'httpClient'));
  assert(getIt.isRegistered<AuthApiService>(instanceName: 'authApiService'));

  // Register NFC services manually
  getIt.registerLazySingleton<NfcService>(() => NfcService());
  getIt.registerLazySingleton<NfcRepository>(() => NfcRepositoryImpl(getIt<NfcService>()));

  // Register Booking services manually
  getIt.registerLazySingleton<BookingService>(() => BookingService());
  getIt.registerLazySingleton<BookingRepository>(() => BookingRepositoryImpl(getIt<BookingService>()));

  await _initializeAsyncDependencies();
}

Future<void> _initializeAsyncDependencies() async {}
