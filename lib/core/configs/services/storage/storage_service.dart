import 'package:escooter/core/configs/constants/shared_prefs_constants.dart';
import 'package:escooter/features/home/<USER>/model/user_model.dart';
import 'package:escooter/features/profile/data/models/payment_preferences_model.dart';
import 'package:escooter/utils/logger.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
// ignore: depend_on_referenced_packages
import 'package:path_provider/path_provider.dart';

@singleton
class StorageService {
  static const String userBoxName = 'userBox';
  static const String isLoggedInKey = 'isLoggedIn';

  Box<UserModel>? _userBox;
  Box<String>? _settingsBox;

  Future<void> initHive() async {
    try {
      if (!Hive.isBoxOpen('userBox')) {
        // Try to initialize Hive with proper path
        try {
          final appDocumentDir = await getApplicationDocumentsDirectory();
          Hive.init(appDocumentDir.path);
        } catch (e) {
          // Fallback for web platform - use default initialization
          AppLogger.log('Using default Hive initialization for web platform');
          await Hive.initFlutter();
        }

        // Register adapters if not registered
        if (!Hive.isAdapterRegistered(0)) {
          Hive.registerAdapter(UserModelAdapter());
        }
        if (!Hive.isAdapterRegistered(1)) {
          Hive.registerAdapter(PaymentPreferencesModelAdapter());
        }

        _userBox = await Hive.openBox<UserModel>('userBox');
        _settingsBox = await Hive.openBox<String>('settingsBox');

        AppLogger.log('Hive initialized successfully');
      }
    } catch (e) {
      AppLogger.error('Failed to initialize Hive: $e');
      // Continue without Hive - use SharedPreferences only
    }
  }

  Box<UserModel>? get userBox => _userBox;
  Box<String>? get settingsBox => _settingsBox;

  // User related methods with Hive fallback
  Future<void> saveUser(UserModel user) async {
    try {
      if (userBox != null) {
        await userBox!.put('currentUser', user);
      } else {
        // Fallback to SharedPreferences for web
        await setString('currentUser', user.toJson().toString());
      }
      await setBool(isLoggedInKey, true);
    } catch (e) {
      AppLogger.error('Error saving user: $e');
      // Fallback to SharedPreferences
      await setString('currentUser', user.toJson().toString());
      await setBool(isLoggedInKey, true);
    }
  }

  UserModel? getUser() {
    try {
      if (userBox != null) {
        return userBox!.get('currentUser');
      } else {
        // Fallback: return a mock user for web platform
        return _getMockUser();
      }
    } catch (e) {
      AppLogger.error('Error getting user: $e');
      // Return mock user as fallback
      return _getMockUser();
    }
  }

  Future<void> deleteUser() async {
    try {
      if (userBox != null) {
        await userBox!.delete('currentUser');
      } else {
        await setString('currentUser', '');
      }
      await setBool(isLoggedInKey, false);
    } catch (e) {
      AppLogger.error('Error deleting user: $e');
      await setBool(isLoggedInKey, false);
    }
  }

  UserModel _getMockUser() {
    return UserModel(
      id: "mock_user_web_001",
      token: "mock_jwt_token_web",
      phoneNumber: "+966501234567",
      firstName: "Ahmed",
      lastName: "Al-Rashid",
      email: "<EMAIL>",
      isVerified: true,
      dateOfBirth: DateTime(1990, 1, 1),
      gender: "male",
      walletBalance: 25.50,
      createdAt: DateTime.now(),
    );
  }

  // Existing SharedPreferences methods
  Future<String?> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(SharedPrefsConstants.languageKey);
  }

  Future<void> setLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(SharedPrefsConstants.languageKey, languageCode);
  }

  Future<bool?> getBool(String key) async {
    final preferences = await SharedPreferences.getInstance();
    return preferences.getBool(key);
  }

  Future<void> setBool(String key, bool value) async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.setBool(key, value);
  }

  Future<String?> getString(String key) async {
    final preferences = await SharedPreferences.getInstance();
    return preferences.getString(key);
  }

  Future<void> setString(String key, String value) async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.setString(key, value);
  }

  Future<List<String>?> getStringList(String key) async {
    final preferences = await SharedPreferences.getInstance();
    return preferences.getStringList(key);
  }

  Future<void> setStringList(String key, List<String> value) async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.setStringList(key, value);
  }

  Future<int?> getInt(String key) async {
    final preferences = await SharedPreferences.getInstance();
    return preferences.getInt(key);
  }

  Future<void> setInt(String key, int value) async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.setInt(key, value);
  }

  Future<double?> getDouble(String key) async {
    final preferences = await SharedPreferences.getInstance();
    return preferences.getDouble(key);
  }

  Future<void> setDouble(String key, double value) async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.setDouble(key, value);
  }

  Future<void> remove(String key) async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.remove(key);
  }

  Future<void> clear() async {
    final preferences = await SharedPreferences.getInstance();
    await preferences.clear();
  }

  // Helper method to check login status
  Future<bool> get isLoggedIn async => await getBool(isLoggedInKey) ?? false;
}
