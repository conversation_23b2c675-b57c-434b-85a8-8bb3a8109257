// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_preferences_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentPreferencesModelAdapter
    extends TypeAdapter<PaymentPreferencesModel> {
  @override
  final int typeId = 1;

  @override
  PaymentPreferencesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentPreferencesModel(
      defaultPaymentMethodId: fields[0] as String,
      autoTopUpEnabled: fields[1] as bool,
      autoTopUpThreshold: fields[2] as double,
      autoTopUpAmount: fields[3] as double,
      preferredPaymentMethods: (fields[4] as List).cast<String>(),
      savePaymentMethods: fields[5] as bool,
      enableQuickPay: fields[6] as bool,
      currency: fields[7] as String,
      lastUpdated: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentPreferencesModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.defaultPaymentMethodId)
      ..writeByte(1)
      ..write(obj.autoTopUpEnabled)
      ..writeByte(2)
      ..write(obj.autoTopUpThreshold)
      ..writeByte(3)
      ..write(obj.autoTopUpAmount)
      ..writeByte(4)
      ..write(obj.preferredPaymentMethods)
      ..writeByte(5)
      ..write(obj.savePaymentMethods)
      ..writeByte(6)
      ..write(obj.enableQuickPay)
      ..writeByte(7)
      ..write(obj.currency)
      ..writeByte(8)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentPreferencesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
