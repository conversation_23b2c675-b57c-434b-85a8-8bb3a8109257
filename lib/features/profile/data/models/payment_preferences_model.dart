import 'package:hive/hive.dart';

part 'payment_preferences_model.g.dart';

@HiveType(typeId: 1)
class PaymentPreferencesModel extends HiveObject {
  @HiveField(0)
  final String defaultPaymentMethodId;

  @HiveField(1)
  final bool autoTopUpEnabled;

  @HiveField(2)
  final double autoTopUpThreshold;

  @HiveField(3)
  final double autoTopUpAmount;

  @HiveField(4)
  final List<String> preferredPaymentMethods;

  @HiveField(5)
  final bool savePaymentMethods;

  @HiveField(6)
  final bool enableQuickPay;

  @HiveField(7)
  final String currency;

  @HiveField(8)
  final DateTime lastUpdated;

  PaymentPreferencesModel({
    required this.defaultPaymentMethodId,
    this.autoTopUpEnabled = false,
    this.autoTopUpThreshold = 10.0,
    this.autoTopUpAmount = 25.0,
    this.preferredPaymentMethods = const [],
    this.savePaymentMethods = true,
    this.enableQuickPay = true,
    this.currency = 'SAR',
    required this.lastUpdated,
  });

  factory PaymentPreferencesModel.defaultPreferences() {
    return PaymentPreferencesModel(
      defaultPaymentMethodId: 'wallet_001',
      autoTopUpEnabled: false,
      autoTopUpThreshold: 10.0,
      autoTopUpAmount: 25.0,
      preferredPaymentMethods: ['wallet_001'],
      savePaymentMethods: true,
      enableQuickPay: true,
      currency: 'SAR',
      lastUpdated: DateTime.now(),
    );
  }

  factory PaymentPreferencesModel.fromJson(Map<String, dynamic> json) {
    return PaymentPreferencesModel(
      defaultPaymentMethodId: json['defaultPaymentMethodId'] as String,
      autoTopUpEnabled: json['autoTopUpEnabled'] as bool? ?? false,
      autoTopUpThreshold: (json['autoTopUpThreshold'] as num?)?.toDouble() ?? 10.0,
      autoTopUpAmount: (json['autoTopUpAmount'] as num?)?.toDouble() ?? 25.0,
      preferredPaymentMethods: List<String>.from(json['preferredPaymentMethods'] ?? []),
      savePaymentMethods: json['savePaymentMethods'] as bool? ?? true,
      enableQuickPay: json['enableQuickPay'] as bool? ?? true,
      currency: json['currency'] as String? ?? 'SAR',
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'defaultPaymentMethodId': defaultPaymentMethodId,
      'autoTopUpEnabled': autoTopUpEnabled,
      'autoTopUpThreshold': autoTopUpThreshold,
      'autoTopUpAmount': autoTopUpAmount,
      'preferredPaymentMethods': preferredPaymentMethods,
      'savePaymentMethods': savePaymentMethods,
      'enableQuickPay': enableQuickPay,
      'currency': currency,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  PaymentPreferencesModel copyWith({
    String? defaultPaymentMethodId,
    bool? autoTopUpEnabled,
    double? autoTopUpThreshold,
    double? autoTopUpAmount,
    List<String>? preferredPaymentMethods,
    bool? savePaymentMethods,
    bool? enableQuickPay,
    String? currency,
    DateTime? lastUpdated,
  }) {
    return PaymentPreferencesModel(
      defaultPaymentMethodId: defaultPaymentMethodId ?? this.defaultPaymentMethodId,
      autoTopUpEnabled: autoTopUpEnabled ?? this.autoTopUpEnabled,
      autoTopUpThreshold: autoTopUpThreshold ?? this.autoTopUpThreshold,
      autoTopUpAmount: autoTopUpAmount ?? this.autoTopUpAmount,
      preferredPaymentMethods: preferredPaymentMethods ?? this.preferredPaymentMethods,
      savePaymentMethods: savePaymentMethods ?? this.savePaymentMethods,
      enableQuickPay: enableQuickPay ?? this.enableQuickPay,
      currency: currency ?? this.currency,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'PaymentPreferencesModel(defaultPaymentMethodId: $defaultPaymentMethodId, autoTopUpEnabled: $autoTopUpEnabled, currency: $currency)';
  }
}
