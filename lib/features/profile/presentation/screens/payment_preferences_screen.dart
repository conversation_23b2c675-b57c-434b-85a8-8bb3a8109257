import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:escooter/features/theme/presentation/providers/theme_provider.dart';
import 'package:escooter/features/profile/presentation/providers/payment_preferences_provider.dart';
import 'package:escooter/features/payment/presentation/providers/payment_method_provider.dart';
import 'package:escooter/features/payment/domain/entities/payment_method.dart';

class PaymentPreferencesScreen extends ConsumerWidget {
  const PaymentPreferencesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeProvider).isDark;
    final theme = Theme.of(context);
    final preferencesAsync = ref.watch(paymentPreferencesNotifierProvider);
    final paymentMethodsAsync = ref.watch(paymentMethodsProvider);

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.grey[50],
      appBar: AppBar(
        backgroundColor: isDarkMode ? Colors.grey[850] : Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Payment Preferences',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
      ),
      body: preferencesAsync.when(
        data: (preferences) => paymentMethodsAsync.when(
          data: (paymentMethods) => _buildContent(
            context,
            ref,
            preferences,
            paymentMethods,
            isDarkMode,
            theme,
          ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorView(context, error.toString()),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorView(context, error.toString()),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    dynamic preferences,
    List<PaymentMethod> paymentMethods,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDefaultPaymentMethodSection(
            context,
            ref,
            preferences,
            paymentMethods,
            isDarkMode,
            theme,
          ),
          const SizedBox(height: 24),
          _buildAutoTopUpSection(
            context,
            ref,
            preferences,
            isDarkMode,
            theme,
          ),
          const SizedBox(height: 24),
          _buildQuickPaySection(
            context,
            ref,
            preferences,
            isDarkMode,
            theme,
          ),
          const SizedBox(height: 24),
          _buildGeneralSettingsSection(
            context,
            ref,
            preferences,
            isDarkMode,
            theme,
          ),
          const SizedBox(height: 32),
          _buildResetButton(context, ref, isDarkMode, theme),
        ],
      ),
    );
  }

  Widget _buildDefaultPaymentMethodSection(
    BuildContext context,
    WidgetRef ref,
    dynamic preferences,
    List<PaymentMethod> paymentMethods,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return _buildSection(
      'Default Payment Method',
      [
        ...paymentMethods.map((method) => RadioListTile<String>(
          title: Text(
            method.displayName,
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          subtitle: method.lastFourDigits != null
              ? Text(
                  '**** ${method.lastFourDigits}',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                )
              : null,
          value: method.id,
          groupValue: preferences.defaultPaymentMethodId,
          onChanged: (value) {
            if (value != null) {
              ref.read(paymentPreferencesNotifierProvider.notifier)
                  .updateDefaultPaymentMethod(value);
            }
          },
          activeColor: theme.colorScheme.primary,
        )),
      ],
      isDarkMode,
      theme,
    );
  }

  Widget _buildAutoTopUpSection(
    BuildContext context,
    WidgetRef ref,
    dynamic preferences,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return _buildSection(
      'Auto Top-Up',
      [
        SwitchListTile(
          title: Text(
            'Enable Auto Top-Up',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          subtitle: Text(
            'Automatically add money when balance is low',
            style: TextStyle(
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
          value: preferences.autoTopUpEnabled,
          onChanged: (value) {
            ref.read(paymentPreferencesNotifierProvider.notifier)
                .updateAutoTopUp(enabled: value);
          },
          activeColor: theme.colorScheme.primary,
        ),
        if (preferences.autoTopUpEnabled) ...[
          ListTile(
            title: Text(
              'Top-up when balance falls below',
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            trailing: Text(
              '${preferences.autoTopUpThreshold.toStringAsFixed(0)} ${preferences.currency}',
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            onTap: () => _showThresholdDialog(context, ref, preferences),
          ),
          ListTile(
            title: Text(
              'Top-up amount',
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            trailing: Text(
              '${preferences.autoTopUpAmount.toStringAsFixed(0)} ${preferences.currency}',
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            onTap: () => _showAmountDialog(context, ref, preferences),
          ),
        ],
      ],
      isDarkMode,
      theme,
    );
  }

  Widget _buildQuickPaySection(
    BuildContext context,
    WidgetRef ref,
    dynamic preferences,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return _buildSection(
      'Quick Pay',
      [
        SwitchListTile(
          title: Text(
            'Enable Quick Pay',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          subtitle: Text(
            'Skip payment confirmation for faster rides',
            style: TextStyle(
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
          value: preferences.enableQuickPay,
          onChanged: (value) {
            ref.read(paymentPreferencesNotifierProvider.notifier)
                .updateGeneralSettings(enableQuickPay: value);
          },
          activeColor: theme.colorScheme.primary,
        ),
      ],
      isDarkMode,
      theme,
    );
  }

  Widget _buildGeneralSettingsSection(
    BuildContext context,
    WidgetRef ref,
    dynamic preferences,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return _buildSection(
      'General Settings',
      [
        SwitchListTile(
          title: Text(
            'Save Payment Methods',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          subtitle: Text(
            'Remember payment methods for future use',
            style: TextStyle(
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
          value: preferences.savePaymentMethods,
          onChanged: (value) {
            ref.read(paymentPreferencesNotifierProvider.notifier)
                .updateGeneralSettings(savePaymentMethods: value);
          },
          activeColor: theme.colorScheme.primary,
        ),
        ListTile(
          title: Text(
            'Currency',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          trailing: Text(
            preferences.currency,
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          onTap: () => _showCurrencyDialog(context, ref, preferences),
        ),
      ],
      isDarkMode,
      theme,
    );
  }

  Widget _buildSection(
    String title,
    List<Widget> children,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return Card(
      color: isDarkMode ? Colors.grey[850] : Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildResetButton(
    BuildContext context,
    WidgetRef ref,
    bool isDarkMode,
    ThemeData theme,
  ) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () => _showResetDialog(context, ref),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          side: BorderSide(color: Colors.red.shade400),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Reset to Defaults',
          style: TextStyle(
            color: Colors.red.shade400,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading preferences',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showThresholdDialog(BuildContext context, WidgetRef ref, dynamic preferences) {
    // Implementation for threshold dialog
  }

  void _showAmountDialog(BuildContext context, WidgetRef ref, dynamic preferences) {
    // Implementation for amount dialog
  }

  void _showCurrencyDialog(BuildContext context, WidgetRef ref, dynamic preferences) {
    // Implementation for currency dialog
  }

  void _showResetDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Preferences'),
        content: const Text('Are you sure you want to reset all payment preferences to default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(paymentPreferencesNotifierProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
