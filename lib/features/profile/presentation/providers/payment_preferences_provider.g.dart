// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_preferences_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$paymentPreferencesNotifierHash() =>
    r'7aba770d513c0a1464379e44ec36f78432701d5f';

/// See also [PaymentPreferencesNotifier].
@ProviderFor(PaymentPreferencesNotifier)
final paymentPreferencesNotifierProvider = AsyncNotifierProvider<
    PaymentPreferencesNotifier, PaymentPreferencesModel>.internal(
  PaymentPreferencesNotifier.new,
  name: r'paymentPreferencesNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$paymentPreferencesNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PaymentPreferencesNotifier = AsyncNotifier<PaymentPreferencesModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
