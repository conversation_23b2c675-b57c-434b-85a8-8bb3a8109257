import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:escooter/core/di/service_locator/service_locator.dart';
import 'package:escooter/core/configs/services/storage/storage_service.dart';
import 'package:escooter/features/profile/data/models/payment_preferences_model.dart';
import 'package:escooter/utils/logger.dart';

part 'payment_preferences_provider.g.dart';

@Riverpod(keepAlive: true)
class PaymentPreferencesNotifier extends _$PaymentPreferencesNotifier {
  @override
  FutureOr<PaymentPreferencesModel> build() async {
    return _loadPaymentPreferences();
  }

  Future<PaymentPreferencesModel> _loadPaymentPreferences() async {
    try {
      final storageService = getIt<StorageService>();
      
      // Try to get saved preferences from storage
      final preferencesJson = await storageService.getString('payment_preferences');
      
      if (preferencesJson != null) {
        final preferencesMap = Map<String, dynamic>.from(
          jsonDecode(preferencesJson)
        );
        return PaymentPreferencesModel.fromJson(preferencesMap);
      }
      
      // Return default preferences if none exist
      final defaultPreferences = PaymentPreferencesModel.defaultPreferences();
      await _savePaymentPreferences(defaultPreferences);
      return defaultPreferences;
    } catch (e) {
      AppLogger.error('Error loading payment preferences: $e');
      return PaymentPreferencesModel.defaultPreferences();
    }
  }

  Future<void> updateDefaultPaymentMethod(String paymentMethodId) async {
    final currentState = await future;
    final updatedPreferences = currentState.copyWith(
      defaultPaymentMethodId: paymentMethodId,
      lastUpdated: DateTime.now(),
    );
    
    await _savePaymentPreferences(updatedPreferences);
    state = AsyncValue.data(updatedPreferences);
  }

  Future<void> updateAutoTopUp({
    bool? enabled,
    double? threshold,
    double? amount,
  }) async {
    final currentState = await future;
    final updatedPreferences = currentState.copyWith(
      autoTopUpEnabled: enabled ?? currentState.autoTopUpEnabled,
      autoTopUpThreshold: threshold ?? currentState.autoTopUpThreshold,
      autoTopUpAmount: amount ?? currentState.autoTopUpAmount,
      lastUpdated: DateTime.now(),
    );
    
    await _savePaymentPreferences(updatedPreferences);
    state = AsyncValue.data(updatedPreferences);
  }

  Future<void> updatePreferredPaymentMethods(List<String> methods) async {
    final currentState = await future;
    final updatedPreferences = currentState.copyWith(
      preferredPaymentMethods: methods,
      lastUpdated: DateTime.now(),
    );
    
    await _savePaymentPreferences(updatedPreferences);
    state = AsyncValue.data(updatedPreferences);
  }

  Future<void> updateGeneralSettings({
    bool? savePaymentMethods,
    bool? enableQuickPay,
    String? currency,
  }) async {
    final currentState = await future;
    final updatedPreferences = currentState.copyWith(
      savePaymentMethods: savePaymentMethods ?? currentState.savePaymentMethods,
      enableQuickPay: enableQuickPay ?? currentState.enableQuickPay,
      currency: currency ?? currentState.currency,
      lastUpdated: DateTime.now(),
    );
    
    await _savePaymentPreferences(updatedPreferences);
    state = AsyncValue.data(updatedPreferences);
  }

  Future<void> resetToDefaults() async {
    final defaultPreferences = PaymentPreferencesModel.defaultPreferences();
    await _savePaymentPreferences(defaultPreferences);
    state = AsyncValue.data(defaultPreferences);
  }

  Future<void> _savePaymentPreferences(PaymentPreferencesModel preferences) async {
    try {
      final storageService = getIt<StorageService>();
      final preferencesJson = jsonEncode(preferences.toJson());
      await storageService.setString('payment_preferences', preferencesJson);
      AppLogger.log('Payment preferences saved successfully');
    } catch (e) {
      AppLogger.error('Error saving payment preferences: $e');
      throw Exception('Failed to save payment preferences');
    }
  }
}

// Convenience providers for specific preference values
final defaultPaymentMethodProvider = Provider<String>((ref) {
  final preferences = ref.watch(paymentPreferencesNotifierProvider);
  return preferences.when(
    data: (prefs) => prefs.defaultPaymentMethodId,
    loading: () => 'wallet_001',
    error: (_, __) => 'wallet_001',
  );
});

final autoTopUpEnabledProvider = Provider<bool>((ref) {
  final preferences = ref.watch(paymentPreferencesNotifierProvider);
  return preferences.when(
    data: (prefs) => prefs.autoTopUpEnabled,
    loading: () => false,
    error: (_, __) => false,
  );
});

final quickPayEnabledProvider = Provider<bool>((ref) {
  final preferences = ref.watch(paymentPreferencesNotifierProvider);
  return preferences.when(
    data: (prefs) => prefs.enableQuickPay,
    loading: () => true,
    error: (_, __) => true,
  );
});
