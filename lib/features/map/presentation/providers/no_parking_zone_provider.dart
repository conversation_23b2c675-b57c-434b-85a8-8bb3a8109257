import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import '../../../../utils/logger.dart';
import '../../data/models/no_parking_zone_model.dart';
import '../../data/services/no_parking_zone_service.dart';
import '../../domain/entities/no_parking_zone.dart';
import '../../../../core/di/service_locator/service_locator.dart';

// Provider for no-parking zones
final noParkingZonesProvider = AsyncNotifierProvider<NoParkingZonesNotifier, List<NoParkingZone>>(() {
  return NoParkingZonesNotifier();
});

// Provider for parking violation check
final parkingViolationProvider = StateProvider<bool>((ref) => false);

// Provider for current location parking status
final currentLocationParkingStatusProvider = StateProvider<ParkingStatus?>((ref) => null);

class NoParkingZonesNotifier extends AsyncNotifier<List<NoParkingZone>> {
  late NoParkingZoneService _service;

  @override
  Future<List<NoParkingZone>> build() async {
    try {
      // For now, we'll create a mock service since we don't have DI setup
      _service = NoParkingZoneService(
        getIt.get(), // http client
        getIt.get(), // storage service
      );
    } catch (e) {
      AppLogger.error('Failed to initialize no-parking zone service', error: e);
      // Return mock data for now
      return NoParkingZoneModel.getMockZones();
    }
    
    return await _fetchNoParkingZones();
  }

  Future<List<NoParkingZone>> _fetchNoParkingZones({
    LatLng? center,
    double? radiusKm,
  }) async {
    try {
      AppLogger.log('NoParkingZonesNotifier: Fetching no-parking zones');
      
      // For now, return mock data since we don't have backend
      final zones = NoParkingZoneModel.getMockZones();
      
      // Filter by location if provided
      if (center != null && radiusKm != null) {
        final filteredZones = zones.where((zone) {
          if (zone.isCircular() && zone.center != null) {
            final distance = Distance().as(LengthUnit.Kilometer, center, zone.center!);
            return distance <= radiusKm;
          }
          
          if (zone.isPolygonal() && zone.coordinates.isNotEmpty) {
            return zone.coordinates.any((coord) {
              final distance = Distance().as(LengthUnit.Kilometer, center, coord);
              return distance <= radiusKm;
            });
          }
          
          return false;
        }).toList();
        
        AppLogger.log('NoParkingZonesNotifier: Found ${filteredZones.length} zones within ${radiusKm}km');
        return filteredZones;
      }
      
      AppLogger.log('NoParkingZonesNotifier: Found ${zones.length} no-parking zones');
      return zones;
    } catch (e) {
      AppLogger.error('NoParkingZonesNotifier: Error fetching no-parking zones', error: e);
      rethrow;
    }
  }

  Future<void> refresh({LatLng? center, double? radiusKm}) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchNoParkingZones(
      center: center,
      radiusKm: radiusKm,
    ));
  }

  Future<bool> checkParkingViolation(LatLng location) async {
    try {
      AppLogger.log('NoParkingZonesNotifier: Checking parking violation');
      
      final zones = state.value ?? [];
      final violatingZones = zones.where((zone) => zone.containsPoint(location)).toList();
      
      if (violatingZones.isNotEmpty) {
        AppLogger.log('⚠️ Parking violation detected in ${violatingZones.length} zones');
        return true;
      }
      
      AppLogger.log('✅ No parking violations detected');
      return false;
    } catch (e) {
      AppLogger.error('NoParkingZonesNotifier: Failed to check parking violation', error: e);
      return false;
    }
  }

  List<NoParkingZone> getZonesContainingPoint(LatLng point) {
    final zones = state.value ?? [];
    return zones.where((zone) => zone.containsPoint(point)).toList();
  }
}

// Provider for checking parking violations at a specific location
final checkParkingViolationProvider = Provider.family<Future<ParkingStatus>, LatLng>((ref, location) async {
  try {
    AppLogger.log('Checking parking status at: ${location.latitude}, ${location.longitude}');
    
    final zonesNotifier = ref.read(noParkingZonesProvider.notifier);
    final hasViolation = await zonesNotifier.checkParkingViolation(location);
    
    if (hasViolation) {
      final violatingZones = zonesNotifier.getZonesContainingPoint(location);
      return ParkingStatus.violation(violatingZones);
    }
    
    return ParkingStatus.allowed();
  } catch (e) {
    AppLogger.error('Error checking parking status', error: e);
    return ParkingStatus.unknown();
  }
});

// Provider for getting zones near a location
final nearbyNoParkingZonesProvider = Provider.family<AsyncValue<List<NoParkingZone>>, LocationQuery>((ref, query) {
  return ref.watch(noParkingZonesProvider).whenData((zones) {
    return zones.where((zone) {
      if (zone.isCircular() && zone.center != null) {
        final distance = Distance().as(LengthUnit.Kilometer, query.center, zone.center!);
        return distance <= query.radiusKm;
      }
      
      if (zone.isPolygonal() && zone.coordinates.isNotEmpty) {
        return zone.coordinates.any((coord) {
          final distance = Distance().as(LengthUnit.Kilometer, query.center, coord);
          return distance <= query.radiusKm;
        });
      }
      
      return false;
    }).toList();
  });
});

// Helper classes
class ParkingStatus {
  final bool isAllowed;
  final bool hasViolation;
  final bool isUnknown;
  final List<NoParkingZone> violatingZones;
  final String? message;

  const ParkingStatus._({
    required this.isAllowed,
    required this.hasViolation,
    required this.isUnknown,
    this.violatingZones = const [],
    this.message,
  });

  factory ParkingStatus.allowed() => const ParkingStatus._(
    isAllowed: true,
    hasViolation: false,
    isUnknown: false,
    message: 'Parking allowed',
  );

  factory ParkingStatus.violation(List<NoParkingZone> zones) => ParkingStatus._(
    isAllowed: false,
    hasViolation: true,
    isUnknown: false,
    violatingZones: zones,
    message: 'Parking not allowed in this area',
  );

  factory ParkingStatus.unknown() => const ParkingStatus._(
    isAllowed: false,
    hasViolation: false,
    isUnknown: true,
    message: 'Unable to determine parking status',
  );
}

class LocationQuery {
  final LatLng center;
  final double radiusKm;

  const LocationQuery({
    required this.center,
    this.radiusKm = 1.0,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationQuery &&
        other.center == center &&
        other.radiusKm == radiusKm;
  }

  @override
  int get hashCode => center.hashCode ^ radiusKm.hashCode;
}
