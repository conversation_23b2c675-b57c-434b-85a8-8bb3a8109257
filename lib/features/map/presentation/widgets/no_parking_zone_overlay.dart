import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import '../../domain/entities/no_parking_zone.dart';
import '../providers/no_parking_zone_provider.dart';

class NoParkingZoneOverlay extends ConsumerWidget {
  final bool showZones;
  final VoidCallback? onZoneTapped;

  const NoParkingZoneOverlay({
    super.key,
    this.showZones = true,
    this.onZoneTapped,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!showZones) return const SizedBox.shrink();

    final noParkingZonesAsync = ref.watch(noParkingZonesProvider);

    return noParkingZonesAsync.when(
      data: (zones) => _buildZoneOverlays(context, zones),
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildZoneOverlays(BuildContext context, List<NoParkingZone> zones) {
    final activeZones = zones.where((zone) => zone.isCurrentlyActive()).toList();

    return Stack(
      children: [
        // Polygon zones
        ...activeZones
            .where((zone) => zone.isPolygonal())
            .map((zone) => _buildPolygonZone(context, zone)),
        
        // Circular zones
        ...activeZones
            .where((zone) => zone.isCircular())
            .map((zone) => _buildCircularZone(context, zone)),
      ],
    );
  }

  Widget _buildPolygonZone(BuildContext context, NoParkingZone zone) {
    return PolygonLayer(
      polygons: [
        Polygon(
          points: zone.coordinates,
          color: _getZoneColor(zone.type).withOpacity(0.3),
          borderColor: _getZoneColor(zone.type),
          borderStrokeWidth: 2,
          isFilled: true,
        ),
      ],
    );
  }

  Widget _buildCircularZone(BuildContext context, NoParkingZone zone) {
    if (zone.center == null || zone.radius == null) {
      return const SizedBox.shrink();
    }

    return CircleLayer(
      circles: [
        CircleMarker(
          point: zone.center!,
          radius: zone.radius!,
          color: _getZoneColor(zone.type).withOpacity(0.3),
          borderColor: _getZoneColor(zone.type),
          borderStrokeWidth: 2,
          useRadiusInMeter: true,
        ),
      ],
    );
  }

  Color _getZoneColor(NoParkingZoneType type) {
    switch (type) {
      case NoParkingZoneType.prohibited:
        return Colors.red;
      case NoParkingZoneType.restricted:
        return Colors.orange;
      case NoParkingZoneType.temporaryRestriction:
        return Colors.yellow;
      case NoParkingZoneType.privateProperty:
        return Colors.purple;
      case NoParkingZoneType.emergencyZone:
        return Colors.red.shade800;
    }
  }
}

class NoParkingZoneMarkers extends ConsumerWidget {
  final VoidCallback? onMarkerTapped;

  const NoParkingZoneMarkers({
    super.key,
    this.onMarkerTapped,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final noParkingZonesAsync = ref.watch(noParkingZonesProvider);

    return noParkingZonesAsync.when(
      data: (zones) => MarkerLayer(
        markers: zones
            .where((zone) => zone.isCurrentlyActive())
            .map((zone) => _buildZoneMarker(context, zone))
            .toList(),
      ),
      loading: () => const MarkerLayer(markers: []),
      error: (error, stack) => const MarkerLayer(markers: []),
    );
  }

  Marker _buildZoneMarker(BuildContext context, NoParkingZone zone) {
    LatLng markerPosition;
    
    if (zone.isCircular() && zone.center != null) {
      markerPosition = zone.center!;
    } else if (zone.isPolygonal() && zone.coordinates.isNotEmpty) {
      // Use center of polygon
      double lat = zone.coordinates.map((p) => p.latitude).reduce((a, b) => a + b) / zone.coordinates.length;
      double lng = zone.coordinates.map((p) => p.longitude).reduce((a, b) => a + b) / zone.coordinates.length;
      markerPosition = LatLng(lat, lng);
    } else {
      return Marker(
        point: const LatLng(0, 0),
        child: const SizedBox.shrink(),
      );
    }

    return Marker(
      point: markerPosition,
      child: GestureDetector(
        onTap: () {
          onMarkerTapped?.call();
          _showZoneInfo(context, zone);
        },
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: _getZoneColor(zone.type),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            _getZoneIcon(zone.type),
            color: Colors.white,
            size: 16,
          ),
        ),
      ),
    );
  }

  Color _getZoneColor(NoParkingZoneType type) {
    switch (type) {
      case NoParkingZoneType.prohibited:
        return Colors.red;
      case NoParkingZoneType.restricted:
        return Colors.orange;
      case NoParkingZoneType.temporaryRestriction:
        return Colors.yellow.shade700;
      case NoParkingZoneType.privateProperty:
        return Colors.purple;
      case NoParkingZoneType.emergencyZone:
        return Colors.red.shade800;
    }
  }

  IconData _getZoneIcon(NoParkingZoneType type) {
    switch (type) {
      case NoParkingZoneType.prohibited:
        return Icons.block;
      case NoParkingZoneType.restricted:
        return Icons.warning;
      case NoParkingZoneType.temporaryRestriction:
        return Icons.schedule;
      case NoParkingZoneType.privateProperty:
        return Icons.business;
      case NoParkingZoneType.emergencyZone:
        return Icons.local_hospital;
    }
  }

  void _showZoneInfo(BuildContext context, NoParkingZone zone) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[900]
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getZoneColor(zone.type),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getZoneIcon(zone.type),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          zone.name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          zone.typeDisplayName,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: _getZoneColor(zone.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                zone.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              if (zone.penalty != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.red, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Penalty: ${zone.penalty}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              if (zone.startTime != null || zone.endTime != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.schedule, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getTimeRestrictionText(zone),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: FilledButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Got it'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTimeRestrictionText(NoParkingZone zone) {
    if (zone.startTime != null && zone.endTime != null) {
      return 'Active: ${_formatTime(zone.startTime!)} - ${_formatTime(zone.endTime!)}';
    } else if (zone.startTime != null) {
      return 'Active from: ${_formatTime(zone.startTime!)}';
    } else if (zone.endTime != null) {
      return 'Active until: ${_formatTime(zone.endTime!)}';
    }
    return 'Time restrictions apply';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
