import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import '../providers/no_parking_zone_provider.dart';
import '../../domain/entities/no_parking_zone.dart';

class ProximityWarningWidget extends ConsumerWidget {
  final LatLng userLocation;
  final VoidCallback? onDismiss;

  const ProximityWarningWidget({
    super.key,
    required this.userLocation,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final warning = ref.watch(proximityWarningProvider(userLocation));
    
    if (warning == null) return const SizedBox.shrink();

    return Positioned(
      top: 100,
      left: 16,
      right: 16,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        child: _buildWarningCard(context, warning),
      ),
    );
  }

  Widget _buildWarningCard(BuildContext context, ProximityWarning warning) {
    final theme = Theme.of(context);
    final color = _getWarningColor(warning.severity);
    
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: color, width: 2),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              color.withOpacity(0.1),
              color.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getWarningIcon(warning.severity),
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    warning.message,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    icon: Icon(Icons.close, color: color),
                    onPressed: onDismiss,
                    iconSize: 20,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              warning.zone.name,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              warning.zone.description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: theme.textTheme.bodySmall?.color,
                ),
                const SizedBox(width: 4),
                Text(
                  '${warning.distance.round()}m away',
                  style: theme.textTheme.bodySmall,
                ),
                const Spacer(),
                if (warning.zone.penalty != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red.withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      'Penalty: ${warning.zone.penalty}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getWarningColor(WarningSeverity severity) {
    switch (severity) {
      case WarningSeverity.info:
        return Colors.blue;
      case WarningSeverity.warning:
        return Colors.orange;
      case WarningSeverity.critical:
        return Colors.red;
    }
  }

  IconData _getWarningIcon(WarningSeverity severity) {
    switch (severity) {
      case WarningSeverity.info:
        return Icons.info_outline;
      case WarningSeverity.warning:
        return Icons.warning_amber;
      case WarningSeverity.critical:
        return Icons.error_outline;
    }
  }
}

class ProximityWarningBanner extends ConsumerWidget {
  final LatLng userLocation;

  const ProximityWarningBanner({
    super.key,
    required this.userLocation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final warning = ref.watch(proximityWarningProvider(userLocation));
    
    if (warning == null || warning.severity != WarningSeverity.critical) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade300),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning,
            color: Colors.red.shade700,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Critical: ${warning.message}',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
