import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:latlong2/latlong.dart';
import '../../../../core/configs/services/storage/storage_service.dart';
import '../../../../core/error/api_exceptions.dart';
import '../../../../utils/logger.dart';
import '../models/no_parking_zone_model.dart';

class NoParkingZoneService {
  final http.Client _client;
  final StorageService _storageService;

  NoParkingZoneService(this._client, this._storageService);

  Future<List<NoParkingZoneModel>> getNoParkingZones({
    LatLng? center,
    double? radiusKm,
  }) async {
    try {
      AppLogger.log('Fetching no-parking zones');

      // For now, return mock data since we don't have backend
      // In production, uncomment the lines below:
      // final response = await _client.get(
      //   Uri.parse('${BaseApi.baseUrl}/v1/no-parking-zones'),
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      // );

      final mockZones = NoParkingZoneModel.getMockZones();
      
      // Filter by location if provided
      if (center != null && radiusKm != null) {
        final filteredZones = mockZones.where((zone) {
          if (zone.isCircular() && zone.center != null) {
            final distance = Distance().as(LengthUnit.Kilometer, center, zone.center!);
            return distance <= radiusKm;
          }
          
          if (zone.isPolygonal() && zone.coordinates.isNotEmpty) {
            // Check if any coordinate is within radius
            return zone.coordinates.any((coord) {
              final distance = Distance().as(LengthUnit.Kilometer, center, coord);
              return distance <= radiusKm;
            });
          }
          
          return false;
        }).toList();
        
        AppLogger.log('Found ${filteredZones.length} no-parking zones within ${radiusKm}km');
        return filteredZones;
      }

      AppLogger.log('Found ${mockZones.length} no-parking zones');
      return mockZones;
    } catch (e) {
      AppLogger.error('Failed to fetch no-parking zones', error: e);
      if (e is ApiException) rethrow;
      throw ApiException('Failed to fetch no-parking zones: $e');
    }
  }

  Future<bool> checkParkingViolation(LatLng location) async {
    try {
      AppLogger.log('Checking parking violation at: ${location.latitude}, ${location.longitude}');

      final zones = await getNoParkingZones(
        center: location,
        radiusKm: 1.0, // Check within 1km radius
      );

      final violatingZones = zones.where((zone) => zone.containsPoint(location)).toList();

      if (violatingZones.isNotEmpty) {
        AppLogger.log('⚠️ Parking violation detected in ${violatingZones.length} zones');
        
        // Store violation locally
        await _storeParkingViolation(location, violatingZones);
        
        return true;
      }

      AppLogger.log('✅ No parking violations detected');
      return false;
    } catch (e) {
      AppLogger.error('Failed to check parking violation', error: e);
      return false;
    }
  }

  Future<void> _storeParkingViolation(
    LatLng location,
    List<NoParkingZoneModel> violatingZones,
  ) async {
    try {
      final violation = {
        'location': {
          'latitude': location.latitude,
          'longitude': location.longitude,
        },
        'zones': violatingZones.map((zone) => zone.toJson()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      final violationsList = await _storageService.getStringList('parking_violations') ?? [];
      violationsList.add(jsonEncode(violation));

      // Keep only last 50 violations
      if (violationsList.length > 50) {
        violationsList.removeRange(0, violationsList.length - 50);
      }

      await _storageService.setStringList('parking_violations', violationsList);
    } catch (e) {
      AppLogger.error('Failed to store parking violation', error: e);
    }
  }

  Future<List<Map<String, dynamic>>> getParkingViolations() async {
    try {
      final violationsJson = await _storageService.getStringList('parking_violations') ?? [];
      return violationsJson
          .map((json) => jsonDecode(json) as Map<String, dynamic>)
          .toList();
    } catch (e) {
      AppLogger.error('Failed to get parking violations', error: e);
      return [];
    }
  }

  Future<bool> reportParkingViolation({
    required LatLng location,
    required String description,
    String? imageUrl,
  }) async {
    try {
      AppLogger.log('Reporting parking violation');

      // In production, this would send to the backend
      final report = {
        'location': {
          'latitude': location.latitude,
          'longitude': location.longitude,
        },
        'description': description,
        'imageUrl': imageUrl,
        'timestamp': DateTime.now().toIso8601String(),
        'reporterId': _storageService.getUser()?.id,
      };

      // Store locally for now
      final reportsList = await _storageService.getStringList('violation_reports') ?? [];
      reportsList.add(jsonEncode(report));

      await _storageService.setStringList('violation_reports', reportsList);

      AppLogger.log('✅ Parking violation reported successfully');
      return true;
    } catch (e) {
      AppLogger.error('Failed to report parking violation', error: e);
      return false;
    }
  }
}
