import 'package:latlong2/latlong.dart';
import '../../domain/entities/no_parking_zone.dart';

class NoParkingZoneModel extends NoParkingZone {
  const NoParkingZoneModel({
    required super.id,
    required super.name,
    required super.description,
    required super.type,
    super.coordinates,
    super.radius,
    super.center,
    super.isActive,
    super.startTime,
    super.endTime,
    super.penalty,
    super.metadata,
  });

  factory NoParkingZoneModel.fromJson(Map<String, dynamic> json) {
    return NoParkingZoneModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: NoParkingZoneType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NoParkingZoneType.restricted,
      ),
      coordinates: (json['coordinates'] as List<dynamic>?)
              ?.map((coord) => LatLng(
                    coord['latitude'] as double,
                    coord['longitude'] as double,
                  ))
              .toList() ??
          [],
      radius: json['radius'] as double?,
      center: json['center'] != null
          ? LatLng(
              json['center']['latitude'] as double,
              json['center']['longitude'] as double,
            )
          : null,
      isActive: json['isActive'] as bool? ?? true,
      startTime: json['startTime'] != null
          ? DateTime.parse(json['startTime'] as String)
          : null,
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      penalty: json['penalty'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  factory NoParkingZoneModel.fromEntity(NoParkingZone entity) {
    return NoParkingZoneModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      type: entity.type,
      coordinates: entity.coordinates,
      radius: entity.radius,
      center: entity.center,
      isActive: entity.isActive,
      startTime: entity.startTime,
      endTime: entity.endTime,
      penalty: entity.penalty,
      metadata: entity.metadata,
    );
  }

  static List<NoParkingZoneModel> getMockZones() {
    // Riyadh coordinates for realistic mock data
    return [
      // King Fahd Road - No parking zone (polygon)
      NoParkingZoneModel(
        id: 'npz_001',
        name: 'King Fahd Road',
        description: 'No parking allowed on King Fahd Road main lanes',
        type: NoParkingZoneType.prohibited,
        coordinates: [
          const LatLng(24.7136, 46.6753),
          const LatLng(24.7140, 46.6755),
          const LatLng(24.7145, 46.6758),
          const LatLng(24.7141, 46.6760),
          const LatLng(24.7136, 46.6753),
        ],
        penalty: 'SAR 300 fine',
        metadata: {'roadType': 'highway', 'enforcement': 'camera'},
      ),
      
      // Kingdom Centre - Circular no parking zone
      NoParkingZoneModel(
        id: 'npz_002',
        name: 'Kingdom Centre Plaza',
        description: 'Private property - no scooter parking',
        type: NoParkingZoneType.privateProperty,
        center: const LatLng(24.7118, 46.6743),
        radius: 100, // 100 meters
        penalty: 'SAR 200 fine + towing',
        metadata: {'propertyType': 'commercial', 'contact': '+966112111000'},
      ),
      
      // King Abdulaziz Hospital - Emergency zone
      NoParkingZoneModel(
        id: 'npz_003',
        name: 'King Abdulaziz Hospital',
        description: 'Emergency zone - keep clear for ambulances',
        type: NoParkingZoneType.emergencyZone,
        center: const LatLng(24.7200, 46.6800),
        radius: 150,
        penalty: 'SAR 500 fine',
        metadata: {'facilityType': 'hospital', 'priority': 'high'},
      ),
      
      // Al Faisaliyah Tower area - Restricted parking
      NoParkingZoneModel(
        id: 'npz_004',
        name: 'Al Faisaliyah Tower Area',
        description: 'Restricted parking during business hours',
        type: NoParkingZoneType.temporaryRestriction,
        coordinates: [
          const LatLng(24.6890, 46.6850),
          const LatLng(24.6895, 46.6855),
          const LatLng(24.6900, 46.6860),
          const LatLng(24.6895, 46.6865),
          const LatLng(24.6890, 46.6860),
          const LatLng(24.6890, 46.6850),
        ],
        startTime: DateTime(2024, 1, 1, 8, 0), // 8 AM
        endTime: DateTime(2024, 1, 1, 18, 0), // 6 PM
        penalty: 'SAR 150 fine',
        metadata: {'businessHours': true, 'weekdaysOnly': true},
      ),
      
      // Riyadh Metro Station - No parking zone
      NoParkingZoneModel(
        id: 'npz_005',
        name: 'Metro Station Entrance',
        description: 'Keep metro station entrances clear',
        type: NoParkingZoneType.restricted,
        center: const LatLng(24.7050, 46.6700),
        radius: 75,
        penalty: 'SAR 250 fine',
        metadata: {'stationType': 'metro', 'line': 'blue'},
      ),
      
      // Diplomatic Quarter - Restricted area
      NoParkingZoneModel(
        id: 'npz_006',
        name: 'Diplomatic Quarter',
        description: 'High security area - no unauthorized parking',
        type: NoParkingZoneType.restricted,
        coordinates: [
          const LatLng(24.6950, 46.6200),
          const LatLng(24.6980, 46.6220),
          const LatLng(24.7000, 46.6250),
          const LatLng(24.6970, 46.6280),
          const LatLng(24.6940, 46.6250),
          const LatLng(24.6950, 46.6200),
        ],
        penalty: 'SAR 1000 fine + security review',
        metadata: {'securityLevel': 'high', 'permits': 'required'},
      ),
    ];
  }
}
