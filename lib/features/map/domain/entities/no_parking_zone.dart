import 'package:latlong2/latlong.dart';

enum NoParkingZoneType {
  restricted,
  prohibited,
  temporaryRestriction,
  privateProperty,
  emergencyZone,
}

class NoParkingZone {
  final String id;
  final String name;
  final String description;
  final NoParkingZoneType type;
  final List<LatLng> coordinates;
  final double? radius; // For circular zones
  final LatLng? center; // For circular zones
  final bool isActive;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? penalty;
  final Map<String, dynamic>? metadata;

  const NoParkingZone({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    this.coordinates = const [],
    this.radius,
    this.center,
    this.isActive = true,
    this.startTime,
    this.endTime,
    this.penalty,
    this.metadata,
  });

  factory NoParkingZone.fromJson(Map<String, dynamic> json) {
    return NoParkingZone(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: NoParkingZoneType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NoParkingZoneType.restricted,
      ),
      coordinates: (json['coordinates'] as List<dynamic>?)
              ?.map((coord) => LatLng(
                    coord['latitude'] as double,
                    coord['longitude'] as double,
                  ))
              .toList() ??
          [],
      radius: json['radius'] as double?,
      center: json['center'] != null
          ? LatLng(
              json['center']['latitude'] as double,
              json['center']['longitude'] as double,
            )
          : null,
      isActive: json['isActive'] as bool? ?? true,
      startTime: json['startTime'] != null
          ? DateTime.parse(json['startTime'] as String)
          : null,
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      penalty: json['penalty'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'coordinates': coordinates
          .map((coord) => {
                'latitude': coord.latitude,
                'longitude': coord.longitude,
              })
          .toList(),
      'radius': radius,
      'center': center != null
          ? {
              'latitude': center!.latitude,
              'longitude': center!.longitude,
            }
          : null,
      'isActive': isActive,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'penalty': penalty,
      'metadata': metadata,
    };
  }

  bool isCircular() => center != null && radius != null;

  bool isPolygonal() => coordinates.isNotEmpty;

  bool isCurrentlyActive() {
    if (!isActive) return false;

    final now = DateTime.now();
    if (startTime != null && now.isBefore(startTime!)) return false;
    if (endTime != null && now.isAfter(endTime!)) return false;

    return true;
  }

  bool containsPoint(LatLng point) {
    if (!isCurrentlyActive()) return false;

    if (isCircular()) {
      final distance = Distance().as(LengthUnit.Meter, center!, point);
      return distance <= radius!;
    }

    if (isPolygonal()) {
      return _isPointInPolygon(point, coordinates);
    }

    return false;
  }

  bool _isPointInPolygon(LatLng point, List<LatLng> polygon) {
    int intersectCount = 0;
    for (int j = 0; j < polygon.length - 1; j++) {
      if (_rayCastIntersect(point, polygon[j], polygon[j + 1])) {
        intersectCount++;
      }
    }

    return (intersectCount % 2) == 1; // odd = inside, even = outside
  }

  bool _rayCastIntersect(LatLng point, LatLng vertA, LatLng vertB) {
    double aY = vertA.latitude;
    double bY = vertB.latitude;
    double aX = vertA.longitude;
    double bX = vertB.longitude;
    double pY = point.latitude;
    double pX = point.longitude;

    if ((aY > pY && bY > pY) || (aY < pY && bY < pY) || (aX < pX && bX < pX)) {
      return false; // a and b can't both be above or below pt.y, and a or
      // b must be east of pt.x
    }

    double m = (aY - bY) / (aX - bX); // Rise over run
    double bee = (-aX) * m + aY; // y = mx + b
    double x = (pY - bee) / m; // algebra is neat!

    return x > pX;
  }

  String get typeDisplayName {
    switch (type) {
      case NoParkingZoneType.restricted:
        return 'Restricted Area';
      case NoParkingZoneType.prohibited:
        return 'No Parking Zone';
      case NoParkingZoneType.temporaryRestriction:
        return 'Temporary Restriction';
      case NoParkingZoneType.privateProperty:
        return 'Private Property';
      case NoParkingZoneType.emergencyZone:
        return 'Emergency Zone';
    }
  }

  NoParkingZone copyWith({
    String? id,
    String? name,
    String? description,
    NoParkingZoneType? type,
    List<LatLng>? coordinates,
    double? radius,
    LatLng? center,
    bool? isActive,
    DateTime? startTime,
    DateTime? endTime,
    String? penalty,
    Map<String, dynamic>? metadata,
  }) {
    return NoParkingZone(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      coordinates: coordinates ?? this.coordinates,
      radius: radius ?? this.radius,
      center: center ?? this.center,
      isActive: isActive ?? this.isActive,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      penalty: penalty ?? this.penalty,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoParkingZone && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NoParkingZone(id: $id, name: $name, type: $type)';
  }
}
