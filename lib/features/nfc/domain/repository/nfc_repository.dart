import 'package:dartz/dartz.dart';
import '../entities/nfc_unlock_result.dart';

abstract class NfcRepository {
  /// Check if NFC is available on the device
  Future<bool> isNfcAvailable();
  
  /// Check if NFC is enabled
  Future<bool> isNfcEnabled();
  
  /// Start NFC scanning session
  Future<Either<String, Stream<NfcUnlockResult>>> startNfcScan();
  
  /// Stop NFC scanning session
  Future<void> stopNfcScan();
  
  /// Validate NFC tag data
  Future<Either<String, String>> validateNfcTag(Map<String, dynamic> tagData);
  
  /// Open device NFC settings
  Future<void> openNfcSettings();
}
