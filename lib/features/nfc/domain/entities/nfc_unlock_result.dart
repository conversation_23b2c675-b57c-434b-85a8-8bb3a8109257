enum NfcUnlockStatus {
  success,
  failed,
  notSupported,
  disabled,
  timeout,
  invalidTag,
  scooterNotFound,
}

class NfcUnlockResult {
  final NfcUnlockStatus status;
  final String? scooterId;
  final String? message;
  final Map<String, dynamic>? metadata;

  const NfcUnlockResult({
    required this.status,
    this.scooterId,
    this.message,
    this.metadata,
  });

  bool get isSuccess => status == NfcUnlockStatus.success;
  bool get isFailure => !isSuccess;

  factory NfcUnlockResult.success({
    required String scooterId,
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.success,
      scooterId: scooterId,
      message: message ?? 'Scooter unlocked successfully via NFC',
      metadata: metadata,
    );
  }

  factory NfcUnlockResult.failed({
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.failed,
      message: message ?? 'Failed to unlock scooter via NFC',
      metadata: metadata,
    );
  }

  factory NfcUnlockResult.notSupported({
    String? message,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.notSupported,
      message: message ?? 'NFC is not supported on this device',
    );
  }

  factory NfcUnlockResult.disabled({
    String? message,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.disabled,
      message: message ?? 'NFC is disabled. Please enable it in settings.',
    );
  }

  factory NfcUnlockResult.timeout({
    String? message,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.timeout,
      message: message ?? 'NFC scan timeout. Please try again.',
    );
  }

  factory NfcUnlockResult.invalidTag({
    String? message,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.invalidTag,
      message: message ?? 'Invalid NFC tag. This is not a valid scooter tag.',
    );
  }

  factory NfcUnlockResult.scooterNotFound({
    String? scooterId,
    String? message,
  }) {
    return NfcUnlockResult(
      status: NfcUnlockStatus.scooterNotFound,
      scooterId: scooterId,
      message: message ?? 'Scooter not found or not available',
    );
  }

  @override
  String toString() {
    return 'NfcUnlockResult(status: $status, scooterId: $scooterId, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NfcUnlockResult &&
        other.status == status &&
        other.scooterId == scooterId &&
        other.message == message;
  }

  @override
  int get hashCode {
    return status.hashCode ^ scooterId.hashCode ^ message.hashCode;
  }
}
