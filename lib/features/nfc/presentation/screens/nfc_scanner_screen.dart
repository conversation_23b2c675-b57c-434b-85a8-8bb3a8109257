import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../common/router/app_router.dart';
import '../../../../utils/logger.dart';
import '../../../safety/presentation/widgets/safety_checklist_modal.dart';
import '../../../rides/domain/usecases/start_ride_usecase.dart';
import '../../../rides/presentation/providers/ride_provider.dart';
import '../../../scanner/presentation/provider/start_ride_provider.dart';
import '../providers/nfc_provider.dart';
import '../widgets/nfc_scanner_overlay.dart';
import '../widgets/nfc_status_indicator.dart';

class NfcScannerScreen extends ConsumerStatefulWidget {
  const NfcScannerScreen({super.key});

  @override
  ConsumerState<NfcScannerScreen> createState() => _NfcScannerScreenState();
}

class _NfcScannerScreenState extends ConsumerState<NfcScannerScreen> {
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _startNfcScan();
  }

  @override
  void dispose() {
    // Note: We can't await in dispose, so we call it without await
    // The NFC provider should handle cleanup gracefully
    _stopNfcScan();
    super.dispose();
  }

  void _startNfcScan() async {
    try {
      AppLogger.log('Starting NFC scan...');
      await ref.read(nfcScannerProvider.notifier).startScan();
    } catch (e) {
      AppLogger.error('Error starting NFC scan', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start NFC scan: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopNfcScan() async {
    try {
      await ref.read(nfcScannerProvider.notifier).stopScan();
    } catch (e) {
      AppLogger.error('Error stopping NFC scan', error: e);
    }
  }

  void _handleNfcResult(String scooterId) async {
    if (_isProcessing) return;

    try {
      setState(() {
        _isProcessing = true;
      });

      AppLogger.log('Scooter ID found via NFC: $scooterId');
      await _stopNfcScan();

      // Show safety checklist before starting ride
      if (mounted) {
        _showSafetyChecklist(scooterId);
      }
    } catch (e) {
      AppLogger.error('Error processing NFC result: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to process NFC scan: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _showSafetyChecklist(String scooterId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => SafetyChecklistModal(
        onCompleted: () => _startRideAfterSafetyCheck(scooterId),
        onSkipped: () => _startRideAfterSafetyCheck(scooterId),
      ),
    );
  }

  Future<void> _startRideAfterSafetyCheck(String scooterId) async {
    try {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Starting your ride...'),
              ],
            ),
          ),
        );
      }

      final result = await ref.read(startRideUseCaseProvider)(
        params: StartRideParams(scooterId: scooterId),
      );

      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      await result.fold(
        (failure) async {
          AppLogger.error('Failed to start ride: ${failure.message}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(failure.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              action: SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: () => _startRideAfterSafetyCheck(scooterId),
              ),
            ),
          );
        },
        (_) async {
          AppLogger.log('Ride started successfully via NFC');
          // Refresh rides data before navigation
          await ref.read(ridesProvider.notifier).refresh();
          // Navigate to active ride screen
          if (mounted) context.go(AppPaths.activeRide);
        },
      );
    } catch (e) {
      AppLogger.error('Error starting ride after safety check', error: e);
      if (!mounted) return;

      // Close loading dialog if open
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to start ride: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final nfcState = ref.watch(nfcScannerProvider);

    // Listen for successful NFC scans
    ref.listen<NfcScannerState>(nfcScannerProvider, (previous, next) {
      if (next.lastResult?.isSuccess == true && !_isProcessing) {
        final scooterId = next.lastResult!.scooterId!;
        _handleNfcResult(scooterId);
      }
    });

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => context.push(AppPaths.home),
        ),
        title: const Text(
          'NFC Scanner',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.qr_code_scanner, color: Colors.white),
            onPressed: () => context.push(AppPaths.scanner),
            tooltip: 'Switch to QR Scanner',
          ),
        ],
      ),
      body: Stack(
        children: [
          // NFC Scanner Overlay
          const Positioned.fill(
            child: NfcScannerOverlay(),
          ),
          
          // Status Indicator
          Positioned(
            top: 20,
            left: 16,
            right: 16,
            child: NfcStatusIndicator(
              isScanning: nfcState.isScanning,
              isProcessing: _isProcessing,
              error: nfcState.error,
            ),
          ),

          // Instructions
          Positioned(
            bottom: 100,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.nfc,
                    color: Colors.blue,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Hold your phone near the scooter\'s NFC tag',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'The NFC tag is usually located on the handlebar or stem',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          // Fallback to QR Scanner button
          Positioned(
            bottom: 20,
            left: 16,
            right: 16,
            child: ElevatedButton.icon(
              onPressed: () => context.push(AppPaths.scanner),
              icon: const Icon(Icons.qr_code_scanner),
              label: const Text('Use QR Scanner Instead'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
