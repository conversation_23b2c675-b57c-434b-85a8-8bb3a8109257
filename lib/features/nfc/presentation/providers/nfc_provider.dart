import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/service_locator/service_locator.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/nfc_unlock_result.dart';
import '../../domain/repository/nfc_repository.dart';

// NFC Scanner State
class NfcScannerState {
  final bool isScanning;
  final bool isAvailable;
  final bool isEnabled;
  final String? error;
  final NfcUnlockResult? lastResult;

  const NfcScannerState({
    this.isScanning = false,
    this.isAvailable = false,
    this.isEnabled = false,
    this.error,
    this.lastResult,
  });

  NfcScannerState copyWith({
    bool? isScanning,
    bool? isAvailable,
    bool? isEnabled,
    String? error,
    NfcUnlockResult? lastResult,
  }) {
    return NfcScannerState(
      isScanning: isScanning ?? this.isScanning,
      isAvailable: isAvailable ?? this.isAvailable,
      isEnabled: isEnabled ?? this.isEnabled,
      error: error,
      lastResult: lastResult ?? this.lastResult,
    );
  }

  @override
  String toString() {
    return 'NfcScannerState(isScanning: $isScanning, isAvailable: $isAvailable, isEnabled: $isEnabled, error: $error)';
  }
}

// NFC Scanner Provider
final nfcScannerProvider = StateNotifierProvider<NfcScannerNotifier, NfcScannerState>((ref) {
  return NfcScannerNotifier();
});

class NfcScannerNotifier extends StateNotifier<NfcScannerState> {
  late final NfcRepository _nfcRepository;
  StreamSubscription<NfcUnlockResult>? _scanSubscription;

  NfcScannerNotifier() : super(const NfcScannerState()) {
    _nfcRepository = getIt<NfcRepository>();
    _initializeNfc();
  }

  Future<void> _initializeNfc() async {
    try {
      AppLogger.log('Initializing NFC...');
      
      final isAvailable = await _nfcRepository.isNfcAvailable();
      final isEnabled = await _nfcRepository.isNfcEnabled();
      
      state = state.copyWith(
        isAvailable: isAvailable,
        isEnabled: isEnabled,
      );
      
      AppLogger.log('NFC initialized - Available: $isAvailable, Enabled: $isEnabled');
    } catch (e) {
      AppLogger.error('Error initializing NFC', error: e);
      state = state.copyWith(
        error: 'Failed to initialize NFC: ${e.toString()}',
      );
    }
  }

  Future<void> startScan() async {
    try {
      if (state.isScanning) {
        AppLogger.log('NFC scan already in progress');
        return;
      }

      AppLogger.log('Starting NFC scan...');
      
      // Check NFC availability and status
      if (!state.isAvailable) {
        throw Exception('NFC is not available on this device');
      }
      
      if (!state.isEnabled) {
        throw Exception('NFC is disabled. Please enable it in settings.');
      }

      state = state.copyWith(
        isScanning: true,
        error: null,
      );

      // Start NFC scanning
      final scanResult = await _nfcRepository.startNfcScan();
      
      scanResult.fold(
        (error) {
          AppLogger.error('Failed to start NFC scan: $error');
          state = state.copyWith(
            isScanning: false,
            error: error,
          );
        },
        (stream) {
          AppLogger.log('NFC scan started successfully');
          _scanSubscription = stream.listen(
            (result) {
              AppLogger.log('NFC scan result: $result');
              state = state.copyWith(
                lastResult: result,
              );
            },
            onError: (error) {
              AppLogger.error('NFC scan error', error: error);
              state = state.copyWith(
                isScanning: false,
                error: error.toString(),
              );
            },
          );
        },
      );
    } catch (e) {
      AppLogger.error('Error starting NFC scan', error: e);
      state = state.copyWith(
        isScanning: false,
        error: e.toString(),
      );
    }
  }

  Future<void> stopScan() async {
    try {
      AppLogger.log('Stopping NFC scan...');
      
      await _scanSubscription?.cancel();
      _scanSubscription = null;
      
      await _nfcRepository.stopNfcScan();
      
      state = state.copyWith(
        isScanning: false,
        error: null,
      );
      
      AppLogger.log('NFC scan stopped');
    } catch (e) {
      AppLogger.error('Error stopping NFC scan', error: e);
      state = state.copyWith(
        isScanning: false,
        error: e.toString(),
      );
    }
  }

  Future<void> openNfcSettings() async {
    try {
      await _nfcRepository.openNfcSettings();
    } catch (e) {
      AppLogger.error('Error opening NFC settings', error: e);
      state = state.copyWith(
        error: 'Failed to open NFC settings: ${e.toString()}',
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearLastResult() {
    state = state.copyWith(lastResult: null);
  }

  @override
  void dispose() {
    _scanSubscription?.cancel();
    _nfcRepository.stopNfcScan();
    super.dispose();
  }
}

// NFC Availability Provider
final nfcAvailabilityProvider = FutureProvider<bool>((ref) async {
  final repository = getIt<NfcRepository>();
  return await repository.isNfcAvailable();
});

// NFC Enabled Provider
final nfcEnabledProvider = FutureProvider<bool>((ref) async {
  final repository = getIt<NfcRepository>();
  return await repository.isNfcEnabled();
});
