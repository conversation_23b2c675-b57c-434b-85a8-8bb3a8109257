import 'dart:math';

class NfcTagModel {
  final String scooterId;
  final String tagId;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const NfcTagModel({
    required this.scooterId,
    required this.tagId,
    required this.timestamp,
    this.metadata = const {},
  });

  factory NfcTagModel.fromJson(Map<String, dynamic> json) {
    return NfcTagModel(
      scooterId: json['scooterId'] as String,
      tagId: json['tagId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'scooterId': scooterId,
      'tagId': tagId,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create a mock NFC tag for testing
  factory NfcTagModel.createMockTag() {
    final random = Random();
    final scooterNumber = 1000 + random.nextInt(9000);
    final tagNumber = 100000 + random.nextInt(900000);
    
    return NfcTagModel(
      scooterId: 'SCTR-$scooterNumber',
      tagId: 'NFC-$tagNumber',
      timestamp: DateTime.now(),
      metadata: {
        'batteryLevel': 70 + random.nextInt(30),
        'location': {
          'latitude': 24.7136 + (random.nextDouble() - 0.5) * 0.01,
          'longitude': 46.6753 + (random.nextDouble() - 0.5) * 0.01,
        },
        'status': 'available',
        'model': 'BarqScoot Pro',
        'version': '2.1',
      },
    );
  }

  @override
  String toString() {
    return 'NfcTagModel(scooterId: $scooterId, tagId: $tagId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NfcTagModel &&
        other.scooterId == scooterId &&
        other.tagId == tagId &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return scooterId.hashCode ^ tagId.hashCode ^ timestamp.hashCode;
  }
}
