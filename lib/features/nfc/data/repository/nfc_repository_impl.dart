import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/nfc_unlock_result.dart';
import '../../domain/repository/nfc_repository.dart';
import '../services/nfc_service.dart';

@Injectable(as: NfcRepository)
class NfcRepositoryImpl implements NfcRepository {
  final NfcService _nfcService;

  NfcRepositoryImpl(this._nfcService);

  @override
  Future<bool> isNfcAvailable() async {
    try {
      return await _nfcService.isNfcAvailable();
    } catch (e) {
      AppLogger.error('Error checking NFC availability', error: e);
      return false;
    }
  }

  @override
  Future<bool> isNfcEnabled() async {
    try {
      return await _nfcService.isNfcEnabled();
    } catch (e) {
      AppLogger.error('Error checking NFC status', error: e);
      return false;
    }
  }

  @override
  Future<Either<String, Stream<NfcUnlockResult>>> startNfcScan() async {
    try {
      AppLogger.log('Starting NFC scan...');
      
      // Check if NFC is available
      final isAvailable = await isNfcAvailable();
      if (!isAvailable) {
        return Left('NFC is not available on this device');
      }

      // Check if NFC is enabled
      final isEnabled = await isNfcEnabled();
      if (!isEnabled) {
        return Left('NFC is disabled. Please enable it in settings.');
      }

      // Start NFC scanning
      final nfcStream = await _nfcService.startNfcScan();
      
      // Transform the raw NFC stream to NfcUnlockResult stream
      final resultStream = nfcStream.asyncMap((tagData) async {
        return await _processNfcTag(tagData);
      });

      return Right(resultStream);
    } catch (e) {
      AppLogger.error('Error starting NFC scan', error: e);
      return Left('Failed to start NFC scan: ${e.toString()}');
    }
  }

  @override
  Future<void> stopNfcScan() async {
    try {
      await _nfcService.stopNfcScan();
      AppLogger.log('NFC scan stopped');
    } catch (e) {
      AppLogger.error('Error stopping NFC scan', error: e);
    }
  }

  @override
  Future<Either<String, String>> validateNfcTag(Map<String, dynamic> tagData) async {
    try {
      final scooterId = await _nfcService.validateNfcTag(tagData);
      if (scooterId != null) {
        return Right(scooterId);
      } else {
        return Left('Invalid NFC tag');
      }
    } catch (e) {
      AppLogger.error('Error validating NFC tag', error: e);
      return Left('Failed to validate NFC tag: ${e.toString()}');
    }
  }

  @override
  Future<void> openNfcSettings() async {
    try {
      await _nfcService.openNfcSettings();
    } catch (e) {
      AppLogger.error('Error opening NFC settings', error: e);
    }
  }

  /// Process NFC tag data and return appropriate result
  Future<NfcUnlockResult> _processNfcTag(Map<String, dynamic> tagData) async {
    try {
      AppLogger.log('Processing NFC tag: $tagData');

      // Validate the tag
      final validationResult = await validateNfcTag(tagData);
      
      return validationResult.fold(
        (error) {
          AppLogger.error('NFC tag validation failed: $error');
          return NfcUnlockResult.invalidTag(message: error);
        },
        (scooterId) {
          AppLogger.log('✅ Valid scooter NFC tag: $scooterId');
          return NfcUnlockResult.success(
            scooterId: scooterId,
            metadata: tagData,
          );
        },
      );
    } catch (e) {
      AppLogger.error('Error processing NFC tag', error: e);
      return NfcUnlockResult.failed(
        message: 'Failed to process NFC tag: ${e.toString()}',
      );
    }
  }
}
