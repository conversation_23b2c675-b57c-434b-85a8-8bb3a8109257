import 'package:dartz/dartz.dart';
import '../entities/scheduled_ride.dart';

abstract class BookingRepository {
  /// Create a new scheduled ride
  Future<Either<String, ScheduledRide>> createScheduledRide(ScheduledRide ride);
  
  /// Get all scheduled rides for the current user
  Future<Either<String, List<ScheduledRide>>> getScheduledRides();
  
  /// Get scheduled rides by status
  Future<Either<String, List<ScheduledRide>>> getScheduledRidesByStatus(ScheduledRideStatus status);
  
  /// Get upcoming scheduled rides (within next 24 hours)
  Future<Either<String, List<ScheduledRide>>> getUpcomingRides();
  
  /// Update a scheduled ride
  Future<Either<String, ScheduledRide>> updateScheduledRide(ScheduledRide ride);
  
  /// Cancel a scheduled ride
  Future<Either<String, void>> cancelScheduledRide(String rideId);
  
  /// Delete a scheduled ride
  Future<Either<String, void>> deleteScheduledRide(String rideId);
  
  /// Check for rides that need notifications
  Future<Either<String, List<ScheduledRide>>> getRidesNeedingNotification();
  
  /// Mark ride as notified
  Future<Either<String, void>> markRideAsNotified(String rideId);
}
