import 'package:latlong2/latlong.dart';

enum ScheduledRideStatus {
  scheduled,
  confirmed,
  cancelled,
  completed,
  expired,
}

enum RideRecurrence {
  none,
  daily,
  weekly,
  monthly,
}

class ScheduledRide {
  final String id;
  final String? scooterId;
  final DateTime scheduledTime;
  final LatLng? pickupLocation;
  final LatLng? dropoffLocation;
  final String? pickupAddress;
  final String? dropoffAddress;
  final ScheduledRideStatus status;
  final RideRecurrence recurrence;
  final DateTime? recurrenceEndDate;
  final String? notes;
  final double? estimatedCost;
  final int? estimatedDuration; // in minutes
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const ScheduledRide({
    required this.id,
    this.scooterId,
    required this.scheduledTime,
    this.pickupLocation,
    this.dropoffLocation,
    this.pickupAddress,
    this.dropoffAddress,
    this.status = ScheduledRideStatus.scheduled,
    this.recurrence = RideRecurrence.none,
    this.recurrenceEndDate,
    this.notes,
    this.estimatedCost,
    this.estimatedDuration,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  factory ScheduledRide.fromJson(Map<String, dynamic> json) {
    return ScheduledRide(
      id: json['id'] as String,
      scooterId: json['scooterId'] as String?,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      pickupLocation: json['pickupLocation'] != null
          ? LatLng(
              json['pickupLocation']['latitude'] as double,
              json['pickupLocation']['longitude'] as double,
            )
          : null,
      dropoffLocation: json['dropoffLocation'] != null
          ? LatLng(
              json['dropoffLocation']['latitude'] as double,
              json['dropoffLocation']['longitude'] as double,
            )
          : null,
      pickupAddress: json['pickupAddress'] as String?,
      dropoffAddress: json['dropoffAddress'] as String?,
      status: ScheduledRideStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ScheduledRideStatus.scheduled,
      ),
      recurrence: RideRecurrence.values.firstWhere(
        (e) => e.name == json['recurrence'],
        orElse: () => RideRecurrence.none,
      ),
      recurrenceEndDate: json['recurrenceEndDate'] != null
          ? DateTime.parse(json['recurrenceEndDate'] as String)
          : null,
      notes: json['notes'] as String?,
      estimatedCost: json['estimatedCost'] as double?,
      estimatedDuration: json['estimatedDuration'] as int?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'scooterId': scooterId,
      'scheduledTime': scheduledTime.toIso8601String(),
      'pickupLocation': pickupLocation != null
          ? {
              'latitude': pickupLocation!.latitude,
              'longitude': pickupLocation!.longitude,
            }
          : null,
      'dropoffLocation': dropoffLocation != null
          ? {
              'latitude': dropoffLocation!.latitude,
              'longitude': dropoffLocation!.longitude,
            }
          : null,
      'pickupAddress': pickupAddress,
      'dropoffAddress': dropoffAddress,
      'status': status.name,
      'recurrence': recurrence.name,
      'recurrenceEndDate': recurrenceEndDate?.toIso8601String(),
      'notes': notes,
      'estimatedCost': estimatedCost,
      'estimatedDuration': estimatedDuration,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  bool get isActive => status == ScheduledRideStatus.scheduled || status == ScheduledRideStatus.confirmed;
  bool get isUpcoming => isActive && scheduledTime.isAfter(DateTime.now());
  bool get isPast => scheduledTime.isBefore(DateTime.now());
  bool get isToday => _isSameDay(scheduledTime, DateTime.now());
  bool get isRecurring => recurrence != RideRecurrence.none;

  Duration get timeUntilRide => scheduledTime.difference(DateTime.now());
  bool get isWithinNotificationWindow => timeUntilRide.inMinutes <= 30 && timeUntilRide.inMinutes > 0;

  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  ScheduledRide copyWith({
    String? id,
    String? scooterId,
    DateTime? scheduledTime,
    LatLng? pickupLocation,
    LatLng? dropoffLocation,
    String? pickupAddress,
    String? dropoffAddress,
    ScheduledRideStatus? status,
    RideRecurrence? recurrence,
    DateTime? recurrenceEndDate,
    String? notes,
    double? estimatedCost,
    int? estimatedDuration,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ScheduledRide(
      id: id ?? this.id,
      scooterId: scooterId ?? this.scooterId,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      dropoffAddress: dropoffAddress ?? this.dropoffAddress,
      status: status ?? this.status,
      recurrence: recurrence ?? this.recurrence,
      recurrenceEndDate: recurrenceEndDate ?? this.recurrenceEndDate,
      notes: notes ?? this.notes,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ScheduledRide(id: $id, scheduledTime: $scheduledTime, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScheduledRide &&
        other.id == id &&
        other.scheduledTime == scheduledTime &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^ scheduledTime.hashCode ^ status.hashCode;
  }
}
