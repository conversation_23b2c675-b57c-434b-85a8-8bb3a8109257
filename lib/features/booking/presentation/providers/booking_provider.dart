import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/service_locator/service_locator.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/scheduled_ride.dart';
import '../../domain/repository/booking_repository.dart';

// Booking State
class BookingState {
  final List<ScheduledRide> scheduledRides;
  final List<ScheduledRide> upcomingRides;
  final bool isLoading;
  final String? error;

  const BookingState({
    this.scheduledRides = const [],
    this.upcomingRides = const [],
    this.isLoading = false,
    this.error,
  });

  BookingState copyWith({
    List<ScheduledRide>? scheduledRides,
    List<ScheduledRide>? upcomingRides,
    bool? isLoading,
    String? error,
  }) {
    return BookingState(
      scheduledRides: scheduledRides ?? this.scheduledRides,
      upcomingRides: upcomingRides ?? this.upcomingRides,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  String toString() {
    return 'BookingState(scheduledRides: ${scheduledRides.length}, upcomingRides: ${upcomingRides.length}, isLoading: $isLoading, error: $error)';
  }
}

// Booking Provider
final bookingProvider = StateNotifierProvider<BookingNotifier, BookingState>((ref) {
  return BookingNotifier();
});

class BookingNotifier extends StateNotifier<BookingState> {
  late final BookingRepository _repository;
  Timer? _notificationTimer;

  BookingNotifier() : super(const BookingState()) {
    _repository = getIt<BookingRepository>();
    _initialize();
  }

  Future<void> _initialize() async {
    await loadScheduledRides();
    _startNotificationTimer();
  }

  /// Load all scheduled rides
  Future<void> loadScheduledRides() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      AppLogger.log('Loading scheduled rides...');
      
      final result = await _repository.getScheduledRides();
      
      result.fold(
        (error) {
          AppLogger.error('Failed to load scheduled rides: $error');
          state = state.copyWith(
            isLoading: false,
            error: error,
          );
        },
        (rides) {
          AppLogger.log('Loaded ${rides.length} scheduled rides');
          state = state.copyWith(
            scheduledRides: rides,
            isLoading: false,
            error: null,
          );
          _loadUpcomingRides();
        },
      );
    } catch (e) {
      AppLogger.error('Error loading scheduled rides', error: e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load upcoming rides
  Future<void> _loadUpcomingRides() async {
    try {
      AppLogger.log('Loading upcoming rides...');
      
      final result = await _repository.getUpcomingRides();
      
      result.fold(
        (error) {
          AppLogger.error('Failed to load upcoming rides: $error');
        },
        (rides) {
          AppLogger.log('Loaded ${rides.length} upcoming rides');
          state = state.copyWith(upcomingRides: rides);
        },
      );
    } catch (e) {
      AppLogger.error('Error loading upcoming rides', error: e);
    }
  }

  /// Create a new scheduled ride
  Future<bool> createScheduledRide(ScheduledRide ride) async {
    try {
      AppLogger.log('Creating scheduled ride...');
      
      final result = await _repository.createScheduledRide(ride);
      
      return result.fold(
        (error) {
          AppLogger.error('Failed to create scheduled ride: $error');
          state = state.copyWith(error: error);
          return false;
        },
        (createdRide) {
          AppLogger.log('Scheduled ride created successfully');
          // Reload rides to get updated list
          loadScheduledRides();
          return true;
        },
      );
    } catch (e) {
      AppLogger.error('Error creating scheduled ride', error: e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Update a scheduled ride
  Future<bool> updateScheduledRide(ScheduledRide ride) async {
    try {
      AppLogger.log('Updating scheduled ride: ${ride.id}');
      
      final result = await _repository.updateScheduledRide(ride);
      
      return result.fold(
        (error) {
          AppLogger.error('Failed to update scheduled ride: $error');
          state = state.copyWith(error: error);
          return false;
        },
        (updatedRide) {
          AppLogger.log('Scheduled ride updated successfully');
          // Update the ride in the current state
          final updatedRides = state.scheduledRides.map((r) {
            return r.id == updatedRide.id ? updatedRide : r;
          }).toList();
          
          state = state.copyWith(scheduledRides: updatedRides);
          _loadUpcomingRides();
          return true;
        },
      );
    } catch (e) {
      AppLogger.error('Error updating scheduled ride', error: e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Cancel a scheduled ride
  Future<bool> cancelScheduledRide(String rideId) async {
    try {
      AppLogger.log('Cancelling scheduled ride: $rideId');
      
      final result = await _repository.cancelScheduledRide(rideId);
      
      return result.fold(
        (error) {
          AppLogger.error('Failed to cancel scheduled ride: $error');
          state = state.copyWith(error: error);
          return false;
        },
        (_) {
          AppLogger.log('Scheduled ride cancelled successfully');
          // Reload rides to get updated list
          loadScheduledRides();
          return true;
        },
      );
    } catch (e) {
      AppLogger.error('Error cancelling scheduled ride', error: e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Delete a scheduled ride
  Future<bool> deleteScheduledRide(String rideId) async {
    try {
      AppLogger.log('Deleting scheduled ride: $rideId');
      
      final result = await _repository.deleteScheduledRide(rideId);
      
      return result.fold(
        (error) {
          AppLogger.error('Failed to delete scheduled ride: $error');
          state = state.copyWith(error: error);
          return false;
        },
        (_) {
          AppLogger.log('Scheduled ride deleted successfully');
          // Remove the ride from the current state
          final updatedRides = state.scheduledRides.where((r) => r.id != rideId).toList();
          final updatedUpcoming = state.upcomingRides.where((r) => r.id != rideId).toList();
          
          state = state.copyWith(
            scheduledRides: updatedRides,
            upcomingRides: updatedUpcoming,
          );
          return true;
        },
      );
    } catch (e) {
      AppLogger.error('Error deleting scheduled ride', error: e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Start notification timer to check for upcoming rides
  void _startNotificationTimer() {
    _notificationTimer?.cancel();
    _notificationTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _checkForNotifications();
    });
  }

  /// Check for rides that need notifications
  Future<void> _checkForNotifications() async {
    try {
      AppLogger.log('Checking for rides needing notifications...');
      
      final result = await _repository.getRidesNeedingNotification();
      
      result.fold(
        (error) {
          AppLogger.error('Failed to check for notifications: $error');
        },
        (rides) {
          if (rides.isNotEmpty) {
            AppLogger.log('Found ${rides.length} rides needing notifications');
            // Here you would trigger actual notifications
            // For now, we'll just log them
            for (final ride in rides) {
              AppLogger.log('📅 Upcoming ride: ${ride.id} at ${ride.scheduledTime}');
            }
          }
        },
      );
    } catch (e) {
      AppLogger.error('Error checking for notifications', error: e);
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  void dispose() {
    _notificationTimer?.cancel();
    super.dispose();
  }
}

// Upcoming Rides Provider (for quick access)
final upcomingRidesProvider = Provider<List<ScheduledRide>>((ref) {
  final bookingState = ref.watch(bookingProvider);
  return bookingState.upcomingRides;
});

// Today's Rides Provider
final todaysRidesProvider = Provider<List<ScheduledRide>>((ref) {
  final bookingState = ref.watch(bookingProvider);
  final today = DateTime.now();
  
  return bookingState.scheduledRides.where((ride) {
    return ride.isToday && ride.isActive;
  }).toList();
});
