import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/scheduled_ride.dart';

class ScheduledRideCard extends StatelessWidget {
  final ScheduledRide ride;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;
  final VoidCallback? onEdit;
  final bool showActions;

  const ScheduledRideCard({
    super.key,
    required this.ride,
    this.onTap,
    this.onCancel,
    this.onEdit,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getStatusColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status and time
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _getStatusColor().withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      _getStatusText(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (ride.isRecurring)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.repeat,
                            size: 12,
                            color: Colors.blue,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            _getRecurrenceText(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.blue,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),

              // Date and time
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _formatDateTime(ride.scheduledTime),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Locations
              if (ride.pickupAddress != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        ride.pickupAddress!,
                        style: theme.textTheme.bodyMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                if (ride.dropoffAddress != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          ride.dropoffAddress!,
                          style: theme.textTheme.bodyMedium,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 8),
              ],

              // Additional info
              Row(
                children: [
                  if (ride.estimatedCost != null) ...[
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                    Text(
                      '\$${ride.estimatedCost!.toStringAsFixed(2)}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (ride.estimatedDuration != null) ...[
                    Icon(
                      Icons.timer,
                      size: 16,
                      color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${ride.estimatedDuration} min',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ],
              ),

              // Notes
              if (ride.notes != null && ride.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.dividerColor.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.note,
                        size: 16,
                        color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          ride.notes!,
                          style: theme.textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Actions
              if (showActions && (onCancel != null || onEdit != null)) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onEdit != null)
                      TextButton.icon(
                        onPressed: onEdit,
                        icon: const Icon(Icons.edit, size: 16),
                        label: const Text('Edit'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.blue,
                        ),
                      ),
                    if (onCancel != null) ...[
                      const SizedBox(width: 8),
                      TextButton.icon(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel, size: 16),
                        label: const Text('Cancel'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (ride.status) {
      case ScheduledRideStatus.scheduled:
        return Colors.blue;
      case ScheduledRideStatus.confirmed:
        return Colors.green;
      case ScheduledRideStatus.cancelled:
        return Colors.red;
      case ScheduledRideStatus.completed:
        return Colors.grey;
      case ScheduledRideStatus.expired:
        return Colors.orange;
    }
  }

  String _getStatusText() {
    switch (ride.status) {
      case ScheduledRideStatus.scheduled:
        return 'Scheduled';
      case ScheduledRideStatus.confirmed:
        return 'Confirmed';
      case ScheduledRideStatus.cancelled:
        return 'Cancelled';
      case ScheduledRideStatus.completed:
        return 'Completed';
      case ScheduledRideStatus.expired:
        return 'Expired';
    }
  }

  String _getRecurrenceText() {
    switch (ride.recurrence) {
      case RideRecurrence.daily:
        return 'Daily';
      case RideRecurrence.weekly:
        return 'Weekly';
      case RideRecurrence.monthly:
        return 'Monthly';
      case RideRecurrence.none:
        return '';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final rideDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (rideDate == today) {
      dateStr = 'Today';
    } else if (rideDate == tomorrow) {
      dateStr = 'Tomorrow';
    } else {
      dateStr = DateFormat('MMM d').format(dateTime);
    }

    final timeStr = DateFormat('h:mm a').format(dateTime);
    return '$dateStr at $timeStr';
  }
}
