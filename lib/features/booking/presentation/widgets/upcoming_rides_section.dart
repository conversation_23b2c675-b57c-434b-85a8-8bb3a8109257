import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/scheduled_ride.dart';
import '../providers/booking_provider.dart';

class UpcomingRidesSection extends ConsumerWidget {
  final VoidCallback? onViewAll;

  const UpcomingRidesSection({
    super.key,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final upcomingRides = ref.watch(upcomingRidesProvider);
    final theme = Theme.of(context);

    if (upcomingRides.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Upcoming Rides',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (onViewAll != null)
                TextButton(
                  onPressed: onViewAll,
                  child: const Text('View All'),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: upcomingRides.length,
            itemBuilder: (context, index) {
              final ride = upcomingRides[index];
              return Container(
                width: 280,
                margin: const EdgeInsets.only(right: 12),
                child: _UpcomingRideCard(ride: ride),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _UpcomingRideCard extends StatelessWidget {
  final ScheduledRide ride;

  const _UpcomingRideCard({required this.ride});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final timeUntil = ride.timeUntilRide;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time and status
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: _getUrgencyColor(timeUntil),
                ),
                const SizedBox(width: 4),
                Text(
                  _formatTimeUntil(timeUntil),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getUrgencyColor(timeUntil),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    _getStatusText(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(),
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Date and time
            Text(
              DateFormat('MMM d, h:mm a').format(ride.scheduledTime),
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            
            // Location
            if (ride.pickupAddress != null)
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 14,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      ride.pickupAddress!,
                      style: theme.textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            
            const Spacer(),
            
            // Cost and duration
            Row(
              children: [
                if (ride.estimatedCost != null) ...[
                  Icon(
                    Icons.attach_money,
                    size: 14,
                    color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                  ),
                  Text(
                    '\$${ride.estimatedCost!.toStringAsFixed(2)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                const Spacer(),
                if (ride.estimatedDuration != null)
                  Text(
                    '${ride.estimatedDuration} min',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getUrgencyColor(Duration timeUntil) {
    if (timeUntil.inMinutes <= 30) {
      return Colors.red;
    } else if (timeUntil.inHours <= 2) {
      return Colors.orange;
    } else {
      return Colors.blue;
    }
  }

  Color _getStatusColor() {
    switch (ride.status) {
      case ScheduledRideStatus.scheduled:
        return Colors.blue;
      case ScheduledRideStatus.confirmed:
        return Colors.green;
      case ScheduledRideStatus.cancelled:
        return Colors.red;
      case ScheduledRideStatus.completed:
        return Colors.grey;
      case ScheduledRideStatus.expired:
        return Colors.orange;
    }
  }

  String _getStatusText() {
    switch (ride.status) {
      case ScheduledRideStatus.scheduled:
        return 'Scheduled';
      case ScheduledRideStatus.confirmed:
        return 'Confirmed';
      case ScheduledRideStatus.cancelled:
        return 'Cancelled';
      case ScheduledRideStatus.completed:
        return 'Completed';
      case ScheduledRideStatus.expired:
        return 'Expired';
    }
  }

  String _formatTimeUntil(Duration timeUntil) {
    if (timeUntil.isNegative) {
      return 'Overdue';
    } else if (timeUntil.inMinutes <= 60) {
      return 'in ${timeUntil.inMinutes}m';
    } else if (timeUntil.inHours <= 24) {
      return 'in ${timeUntil.inHours}h';
    } else {
      return 'in ${timeUntil.inDays}d';
    }
  }
}
