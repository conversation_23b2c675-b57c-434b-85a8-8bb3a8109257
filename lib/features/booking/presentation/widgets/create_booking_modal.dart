import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import '../../domain/entities/scheduled_ride.dart';

class CreateBookingModal extends StatefulWidget {
  final Function(ScheduledRide) onRideScheduled;

  const CreateBookingModal({
    super.key,
    required this.onRideScheduled,
  });

  @override
  State<CreateBookingModal> createState() => _CreateBookingModalState();
}

class _CreateBookingModalState extends State<CreateBookingModal> {
  final _formKey = GlobalKey<FormState>();
  final _pickupController = TextEditingController();
  final _dropoffController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now().add(const Duration(hours: 1));
  TimeOfDay _selectedTime = TimeOfDay.now();
  RideRecurrence _recurrence = RideRecurrence.none;
  DateTime? _recurrenceEndDate;

  @override
  void initState() {
    super.initState();
    // Round to next hour
    final now = DateTime.now();
    _selectedDate = DateTime(now.year, now.month, now.day, now.hour + 1);
    _selectedTime = TimeOfDay.fromDateTime(_selectedDate);
  }

  @override
  void dispose() {
    _pickupController.dispose();
    _dropoffController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);

    return Container(
      height: mediaQuery.size.height * 0.9,
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'Schedule a Ride',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Form
          Expanded(
            child: Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(20),
                children: [
                  // Date and Time Section
                  _buildSectionTitle('When'),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateField(),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTimeField(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Recurrence Section
                  _buildSectionTitle('Repeat'),
                  const SizedBox(height: 12),
                  _buildRecurrenceField(),
                  if (_recurrence != RideRecurrence.none) ...[
                    const SizedBox(height: 12),
                    _buildRecurrenceEndDateField(),
                  ],
                  const SizedBox(height: 24),

                  // Location Section
                  _buildSectionTitle('Where'),
                  const SizedBox(height: 12),
                  _buildLocationField(
                    controller: _pickupController,
                    label: 'Pickup Location',
                    icon: Icons.location_on,
                    iconColor: Colors.green,
                  ),
                  const SizedBox(height: 16),
                  _buildLocationField(
                    controller: _dropoffController,
                    label: 'Drop-off Location (Optional)',
                    icon: Icons.location_on,
                    iconColor: Colors.red,
                    required: false,
                  ),
                  const SizedBox(height: 24),

                  // Notes Section
                  _buildSectionTitle('Notes'),
                  const SizedBox(height: 12),
                  _buildNotesField(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          
          // Action Buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(color: theme.dividerColor),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _scheduleRide,
                    child: const Text('Schedule Ride'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildDateField() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Date',
          prefixIcon: Icon(Icons.calendar_today),
          border: OutlineInputBorder(),
        ),
        child: Text(
          DateFormat('MMM d, yyyy').format(_selectedDate),
        ),
      ),
    );
  }

  Widget _buildTimeField() {
    return InkWell(
      onTap: _selectTime,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Time',
          prefixIcon: Icon(Icons.access_time),
          border: OutlineInputBorder(),
        ),
        child: Text(
          _selectedTime.format(context),
        ),
      ),
    );
  }

  Widget _buildRecurrenceField() {
    return DropdownButtonFormField<RideRecurrence>(
      value: _recurrence,
      decoration: const InputDecoration(
        labelText: 'Repeat',
        prefixIcon: Icon(Icons.repeat),
        border: OutlineInputBorder(),
      ),
      items: RideRecurrence.values.map((recurrence) {
        return DropdownMenuItem(
          value: recurrence,
          child: Text(_getRecurrenceText(recurrence)),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _recurrence = value ?? RideRecurrence.none;
          if (_recurrence == RideRecurrence.none) {
            _recurrenceEndDate = null;
          }
        });
      },
    );
  }

  Widget _buildRecurrenceEndDateField() {
    return InkWell(
      onTap: _selectRecurrenceEndDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Repeat Until',
          prefixIcon: Icon(Icons.event),
          border: OutlineInputBorder(),
        ),
        child: Text(
          _recurrenceEndDate != null
              ? DateFormat('MMM d, yyyy').format(_recurrenceEndDate!)
              : 'Select end date',
        ),
      ),
    );
  }

  Widget _buildLocationField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required Color iconColor,
    bool required = true,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: iconColor),
        border: const OutlineInputBorder(),
        suffixIcon: IconButton(
          icon: const Icon(Icons.my_location),
          onPressed: () {
            // TODO: Implement current location
          },
        ),
      ),
      validator: required
          ? (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a pickup location';
              }
              return null;
            }
          : null,
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'Notes (Optional)',
        prefixIcon: Icon(Icons.note),
        border: OutlineInputBorder(),
        hintText: 'Any special instructions...',
      ),
      maxLines: 3,
    );
  }

  Future<void> _selectDate() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );
    
    if (picked != null) {
      setState(() {
        _selectedDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          _selectedDate.hour,
          _selectedDate.minute,
        );
      });
    }
  }

  Future<void> _selectTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    
    if (picked != null) {
      setState(() {
        _selectedTime = picked;
        _selectedDate = DateTime(
          _selectedDate.year,
          _selectedDate.month,
          _selectedDate.day,
          picked.hour,
          picked.minute,
        );
      });
    }
  }

  Future<void> _selectRecurrenceEndDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _recurrenceEndDate ?? _selectedDate.add(const Duration(days: 30)),
      firstDate: _selectedDate,
      lastDate: _selectedDate.add(const Duration(days: 365)),
    );
    
    if (picked != null) {
      setState(() {
        _recurrenceEndDate = picked;
      });
    }
  }

  String _getRecurrenceText(RideRecurrence recurrence) {
    switch (recurrence) {
      case RideRecurrence.none:
        return 'No repeat';
      case RideRecurrence.daily:
        return 'Daily';
      case RideRecurrence.weekly:
        return 'Weekly';
      case RideRecurrence.monthly:
        return 'Monthly';
    }
  }

  void _scheduleRide() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Create the scheduled ride
    final ride = ScheduledRide(
      id: 'SCH-${DateTime.now().millisecondsSinceEpoch}',
      scheduledTime: _selectedDate,
      pickupAddress: _pickupController.text.trim(),
      dropoffAddress: _dropoffController.text.trim().isNotEmpty
          ? _dropoffController.text.trim()
          : null,
      pickupLocation: const LatLng(24.7136, 46.6753), // Mock location
      dropoffLocation: _dropoffController.text.trim().isNotEmpty
          ? const LatLng(24.7200, 46.6800) // Mock location
          : null,
      recurrence: _recurrence,
      recurrenceEndDate: _recurrenceEndDate,
      notes: _notesController.text.trim().isNotEmpty
          ? _notesController.text.trim()
          : null,
      estimatedCost: 25.0, // Mock cost
      estimatedDuration: 20, // Mock duration
      createdAt: DateTime.now(),
    );

    widget.onRideScheduled(ride);
    Navigator.of(context).pop();
  }
}
