import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../common/router/app_router.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/scheduled_ride.dart';
import '../providers/booking_provider.dart';
import '../widgets/scheduled_ride_card.dart';
import '../widgets/create_booking_modal.dart';
import '../widgets/upcoming_rides_section.dart';

class BookingSchedulerScreen extends ConsumerStatefulWidget {
  const BookingSchedulerScreen({super.key});

  @override
  ConsumerState<BookingSchedulerScreen> createState() => _BookingSchedulerScreenState();
}

class _BookingSchedulerScreenState extends ConsumerState<BookingSchedulerScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bookingState = ref.watch(bookingProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ride Scheduler'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.push(AppPaths.home),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(bookingProvider.notifier).loadScheduledRides(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Upcoming', icon: Icon(Icons.schedule)),
            Tab(text: 'All Rides', icon: Icon(Icons.list)),
            Tab(text: 'History', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: bookingState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : bookingState.error != null
              ? _buildErrorView(bookingState.error!)
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildUpcomingTab(),
                    _buildAllRidesTab(),
                    _buildHistoryTab(),
                  ],
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateBookingModal,
        icon: const Icon(Icons.add),
        label: const Text('Schedule Ride'),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading scheduled rides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.read(bookingProvider.notifier).loadScheduledRides(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingTab() {
    final upcomingRides = ref.watch(upcomingRidesProvider);
    final todaysRides = ref.watch(todaysRidesProvider);

    return RefreshIndicator(
      onRefresh: () => ref.read(bookingProvider.notifier).loadScheduledRides(),
      child: CustomScrollView(
        slivers: [
          if (todaysRides.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Today\'s Rides',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final ride = todaysRides[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: ScheduledRideCard(
                      ride: ride,
                      onTap: () => _showRideDetails(ride),
                      onCancel: () => _cancelRide(ride),
                      onEdit: () => _editRide(ride),
                    ),
                  );
                },
                childCount: todaysRides.length,
              ),
            ),
          ],
          if (upcomingRides.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Upcoming Rides',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final ride = upcomingRides[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: ScheduledRideCard(
                      ride: ride,
                      onTap: () => _showRideDetails(ride),
                      onCancel: () => _cancelRide(ride),
                      onEdit: () => _editRide(ride),
                    ),
                  );
                },
                childCount: upcomingRides.length,
              ),
            ),
          ],
          if (upcomingRides.isEmpty && todaysRides.isEmpty)
            SliverFillRemaining(
              child: _buildEmptyState('No upcoming rides scheduled'),
            ),
        ],
      ),
    );
  }

  Widget _buildAllRidesTab() {
    final bookingState = ref.watch(bookingProvider);
    final allRides = bookingState.scheduledRides;

    if (allRides.isEmpty) {
      return _buildEmptyState('No scheduled rides found');
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(bookingProvider.notifier).loadScheduledRides(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: allRides.length,
        itemBuilder: (context, index) {
          final ride = allRides[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: ScheduledRideCard(
              ride: ride,
              onTap: () => _showRideDetails(ride),
              onCancel: ride.isActive ? () => _cancelRide(ride) : null,
              onEdit: ride.isActive ? () => _editRide(ride) : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildHistoryTab() {
    final bookingState = ref.watch(bookingProvider);
    final historyRides = bookingState.scheduledRides
        .where((ride) => 
            ride.status == ScheduledRideStatus.completed ||
            ride.status == ScheduledRideStatus.cancelled ||
            ride.status == ScheduledRideStatus.expired)
        .toList();

    if (historyRides.isEmpty) {
      return _buildEmptyState('No ride history found');
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(bookingProvider.notifier).loadScheduledRides(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: historyRides.length,
        itemBuilder: (context, index) {
          final ride = historyRides[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: ScheduledRideCard(
              ride: ride,
              onTap: () => _showRideDetails(ride),
              showActions: false,
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to schedule your first ride',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateBookingModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateBookingModal(
        onRideScheduled: (ride) {
          ref.read(bookingProvider.notifier).createScheduledRide(ride);
        },
      ),
    );
  }

  void _showRideDetails(ScheduledRide ride) {
    // TODO: Implement ride details screen
    AppLogger.log('Show ride details: ${ride.id}');
  }

  void _editRide(ScheduledRide ride) {
    // TODO: Implement edit ride functionality
    AppLogger.log('Edit ride: ${ride.id}');
  }

  void _cancelRide(ScheduledRide ride) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Ride'),
        content: Text('Are you sure you want to cancel this scheduled ride?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(bookingProvider.notifier).cancelScheduledRide(ride.id);
            },
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }
}
