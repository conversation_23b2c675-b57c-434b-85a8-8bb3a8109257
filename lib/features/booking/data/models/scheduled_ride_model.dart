import 'dart:math';
import 'package:latlong2/latlong.dart';
import '../../domain/entities/scheduled_ride.dart';

class ScheduledRideModel extends ScheduledRide {
  const ScheduledRideModel({
    required super.id,
    super.scooterId,
    required super.scheduledTime,
    super.pickupLocation,
    super.dropoffLocation,
    super.pickupAddress,
    super.dropoffAddress,
    super.status,
    super.recurrence,
    super.recurrenceEndDate,
    super.notes,
    super.estimatedCost,
    super.estimatedDuration,
    required super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory ScheduledRideModel.fromJson(Map<String, dynamic> json) {
    return ScheduledRideModel(
      id: json['id'] as String,
      scooterId: json['scooterId'] as String?,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      pickupLocation: json['pickupLocation'] != null
          ? LatLng(
              json['pickupLocation']['latitude'] as double,
              json['pickupLocation']['longitude'] as double,
            )
          : null,
      dropoffLocation: json['dropoffLocation'] != null
          ? LatLng(
              json['dropoffLocation']['latitude'] as double,
              json['dropoffLocation']['longitude'] as double,
            )
          : null,
      pickupAddress: json['pickupAddress'] as String?,
      dropoffAddress: json['dropoffAddress'] as String?,
      status: ScheduledRideStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ScheduledRideStatus.scheduled,
      ),
      recurrence: RideRecurrence.values.firstWhere(
        (e) => e.name == json['recurrence'],
        orElse: () => RideRecurrence.none,
      ),
      recurrenceEndDate: json['recurrenceEndDate'] != null
          ? DateTime.parse(json['recurrenceEndDate'] as String)
          : null,
      notes: json['notes'] as String?,
      estimatedCost: json['estimatedCost'] as double?,
      estimatedDuration: json['estimatedDuration'] as int?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  factory ScheduledRideModel.fromEntity(ScheduledRide ride) {
    return ScheduledRideModel(
      id: ride.id,
      scooterId: ride.scooterId,
      scheduledTime: ride.scheduledTime,
      pickupLocation: ride.pickupLocation,
      dropoffLocation: ride.dropoffLocation,
      pickupAddress: ride.pickupAddress,
      dropoffAddress: ride.dropoffAddress,
      status: ride.status,
      recurrence: ride.recurrence,
      recurrenceEndDate: ride.recurrenceEndDate,
      notes: ride.notes,
      estimatedCost: ride.estimatedCost,
      estimatedDuration: ride.estimatedDuration,
      createdAt: ride.createdAt,
      updatedAt: ride.updatedAt,
      metadata: ride.metadata,
    );
  }

  /// Create a mock scheduled ride for testing
  factory ScheduledRideModel.createMock({
    DateTime? scheduledTime,
    ScheduledRideStatus? status,
    RideRecurrence? recurrence,
  }) {
    final random = Random();
    final now = DateTime.now();
    final rideId = 'SCH-${1000 + random.nextInt(9000)}';
    final scooterId = 'SCTR-${1000 + random.nextInt(9000)}';
    
    // Mock locations in Riyadh area
    final pickupLat = 24.7136 + (random.nextDouble() - 0.5) * 0.02;
    final pickupLng = 46.6753 + (random.nextDouble() - 0.5) * 0.02;
    final dropoffLat = pickupLat + (random.nextDouble() - 0.5) * 0.01;
    final dropoffLng = pickupLng + (random.nextDouble() - 0.5) * 0.01;
    
    final addresses = [
      'King Fahd Road, Riyadh',
      'Olaya Street, Riyadh',
      'Prince Mohammed Bin Abdulaziz Road',
      'King Abdulaziz Road, Riyadh',
      'Al Tahlia Street, Riyadh',
      'King Khalid International Airport',
      'Riyadh Park Mall',
      'Kingdom Centre',
      'Al Faisaliyah Centre',
      'King Saud University',
    ];
    
    return ScheduledRideModel(
      id: rideId,
      scooterId: scooterId,
      scheduledTime: scheduledTime ?? now.add(Duration(hours: random.nextInt(48))),
      pickupLocation: LatLng(pickupLat, pickupLng),
      dropoffLocation: LatLng(dropoffLat, dropoffLng),
      pickupAddress: addresses[random.nextInt(addresses.length)],
      dropoffAddress: addresses[random.nextInt(addresses.length)],
      status: status ?? ScheduledRideStatus.scheduled,
      recurrence: recurrence ?? RideRecurrence.none,
      recurrenceEndDate: recurrence != null && recurrence != RideRecurrence.none
          ? now.add(Duration(days: 30 + random.nextInt(60)))
          : null,
      notes: random.nextBool() ? 'Please wait at the main entrance' : null,
      estimatedCost: 15.0 + random.nextDouble() * 25.0,
      estimatedDuration: 15 + random.nextInt(30),
      createdAt: now.subtract(Duration(hours: random.nextInt(24))),
      metadata: {
        'source': 'mock',
        'priority': random.nextBool() ? 'high' : 'normal',
        'vehicleType': 'electric_scooter',
      },
    );
  }

  @override
  ScheduledRideModel copyWith({
    String? id,
    String? scooterId,
    DateTime? scheduledTime,
    LatLng? pickupLocation,
    LatLng? dropoffLocation,
    String? pickupAddress,
    String? dropoffAddress,
    ScheduledRideStatus? status,
    RideRecurrence? recurrence,
    DateTime? recurrenceEndDate,
    String? notes,
    double? estimatedCost,
    int? estimatedDuration,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ScheduledRideModel(
      id: id ?? this.id,
      scooterId: scooterId ?? this.scooterId,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      dropoffAddress: dropoffAddress ?? this.dropoffAddress,
      status: status ?? this.status,
      recurrence: recurrence ?? this.recurrence,
      recurrenceEndDate: recurrenceEndDate ?? this.recurrenceEndDate,
      notes: notes ?? this.notes,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}
