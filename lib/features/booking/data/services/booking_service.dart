import 'dart:convert';
import 'dart:math';
import 'package:injectable/injectable.dart';
import '../../../../utils/logger.dart';
import '../models/scheduled_ride_model.dart';
import '../../domain/entities/scheduled_ride.dart';

@injectable
class BookingService {
  // Mock storage for scheduled rides
  final List<ScheduledRideModel> _scheduledRides = [];

  /// Create a new scheduled ride
  Future<ScheduledRideModel> createScheduledRide(ScheduledRideModel ride) async {
    try {
      AppLogger.log('Creating scheduled ride: ${ride.id}');
      
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Add to mock storage
      _scheduledRides.add(ride);
      
      AppLogger.log('✅ Scheduled ride created successfully: ${ride.id}');
      return ride;
    } catch (e) {
      AppLogger.error('Failed to create scheduled ride', error: e);
      rethrow;
    }
  }

  /// Get all scheduled rides
  Future<List<ScheduledRideModel>> getScheduledRides() async {
    try {
      AppLogger.log('Fetching scheduled rides...');
      
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 300));
      
      // If no rides exist, create some mock data
      if (_scheduledRides.isEmpty) {
        _createMockScheduledRides();
      }
      
      AppLogger.log('Found ${_scheduledRides.length} scheduled rides');
      return List.from(_scheduledRides);
    } catch (e) {
      AppLogger.error('Failed to fetch scheduled rides', error: e);
      rethrow;
    }
  }

  /// Get scheduled rides by status
  Future<List<ScheduledRideModel>> getScheduledRidesByStatus(ScheduledRideStatus status) async {
    try {
      AppLogger.log('Fetching scheduled rides with status: $status');
      
      final allRides = await getScheduledRides();
      final filteredRides = allRides.where((ride) => ride.status == status).toList();
      
      AppLogger.log('Found ${filteredRides.length} rides with status: $status');
      return filteredRides;
    } catch (e) {
      AppLogger.error('Failed to fetch rides by status', error: e);
      rethrow;
    }
  }

  /// Get upcoming rides (within next 24 hours)
  Future<List<ScheduledRideModel>> getUpcomingRides() async {
    try {
      AppLogger.log('Fetching upcoming rides...');
      
      final allRides = await getScheduledRides();
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(hours: 24));
      
      final upcomingRides = allRides.where((ride) {
        return ride.isActive &&
            ride.scheduledTime.isAfter(now) &&
            ride.scheduledTime.isBefore(tomorrow);
      }).toList();
      
      // Sort by scheduled time
      upcomingRides.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
      
      AppLogger.log('Found ${upcomingRides.length} upcoming rides');
      return upcomingRides;
    } catch (e) {
      AppLogger.error('Failed to fetch upcoming rides', error: e);
      rethrow;
    }
  }

  /// Update a scheduled ride
  Future<ScheduledRideModel> updateScheduledRide(ScheduledRideModel ride) async {
    try {
      AppLogger.log('Updating scheduled ride: ${ride.id}');
      
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 400));
      
      // Find and update the ride
      final index = _scheduledRides.indexWhere((r) => r.id == ride.id);
      if (index != -1) {
        _scheduledRides[index] = ride.copyWith(updatedAt: DateTime.now());
        AppLogger.log('✅ Scheduled ride updated successfully: ${ride.id}');
        return _scheduledRides[index];
      } else {
        throw Exception('Scheduled ride not found: ${ride.id}');
      }
    } catch (e) {
      AppLogger.error('Failed to update scheduled ride', error: e);
      rethrow;
    }
  }

  /// Cancel a scheduled ride
  Future<void> cancelScheduledRide(String rideId) async {
    try {
      AppLogger.log('Cancelling scheduled ride: $rideId');
      
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 300));
      
      // Find and update the ride status
      final index = _scheduledRides.indexWhere((r) => r.id == rideId);
      if (index != -1) {
        _scheduledRides[index] = _scheduledRides[index].copyWith(
          status: ScheduledRideStatus.cancelled,
          updatedAt: DateTime.now(),
        );
        AppLogger.log('✅ Scheduled ride cancelled successfully: $rideId');
      } else {
        throw Exception('Scheduled ride not found: $rideId');
      }
    } catch (e) {
      AppLogger.error('Failed to cancel scheduled ride', error: e);
      rethrow;
    }
  }

  /// Delete a scheduled ride
  Future<void> deleteScheduledRide(String rideId) async {
    try {
      AppLogger.log('Deleting scheduled ride: $rideId');
      
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 300));
      
      // Remove the ride
      _scheduledRides.removeWhere((r) => r.id == rideId);
      AppLogger.log('✅ Scheduled ride deleted successfully: $rideId');
    } catch (e) {
      AppLogger.error('Failed to delete scheduled ride', error: e);
      rethrow;
    }
  }

  /// Get rides that need notifications (within 30 minutes)
  Future<List<ScheduledRideModel>> getRidesNeedingNotification() async {
    try {
      AppLogger.log('Checking for rides needing notification...');
      
      final allRides = await getScheduledRides();
      final now = DateTime.now();
      
      final ridesNeedingNotification = allRides.where((ride) {
        return ride.isActive && ride.isWithinNotificationWindow;
      }).toList();
      
      AppLogger.log('Found ${ridesNeedingNotification.length} rides needing notification');
      return ridesNeedingNotification;
    } catch (e) {
      AppLogger.error('Failed to check rides needing notification', error: e);
      rethrow;
    }
  }

  /// Create mock scheduled rides for demo
  void _createMockScheduledRides() {
    final now = DateTime.now();
    final random = Random();
    
    final mockRides = [
      ScheduledRideModel.createMock(
        scheduledTime: now.add(const Duration(hours: 2)),
        status: ScheduledRideStatus.scheduled,
      ),
      ScheduledRideModel.createMock(
        scheduledTime: now.add(const Duration(days: 1, hours: 8)),
        status: ScheduledRideStatus.confirmed,
        recurrence: RideRecurrence.daily,
      ),
      ScheduledRideModel.createMock(
        scheduledTime: now.add(const Duration(minutes: 15)),
        status: ScheduledRideStatus.scheduled,
      ),
      ScheduledRideModel.createMock(
        scheduledTime: now.add(const Duration(days: 3, hours: 9)),
        status: ScheduledRideStatus.scheduled,
        recurrence: RideRecurrence.weekly,
      ),
      ScheduledRideModel.createMock(
        scheduledTime: now.subtract(const Duration(hours: 1)),
        status: ScheduledRideStatus.completed,
      ),
    ];
    
    _scheduledRides.addAll(mockRides);
    AppLogger.log('Created ${mockRides.length} mock scheduled rides');
  }
}
