import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../utils/logger.dart';
import '../../domain/entities/scheduled_ride.dart';
import '../../domain/repository/booking_repository.dart';
import '../models/scheduled_ride_model.dart';
import '../services/booking_service.dart';

@Injectable(as: BookingRepository)
class BookingRepositoryImpl implements BookingRepository {
  final BookingService _bookingService;

  BookingRepositoryImpl(this._bookingService);

  @override
  Future<Either<String, ScheduledRide>> createScheduledRide(ScheduledRide ride) async {
    try {
      AppLogger.log('Repository: Creating scheduled ride');
      
      final rideModel = ScheduledRideModel.fromEntity(ride);
      final result = await _bookingService.createScheduledRide(rideModel);
      
      AppLogger.log('Repository: Scheduled ride created successfully');
      return Right(result);
    } catch (e) {
      AppLogger.error('Repository: Failed to create scheduled ride', error: e);
      return Left('Failed to create scheduled ride: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, List<ScheduledRide>>> getScheduledRides() async {
    try {
      AppLogger.log('Repository: Fetching scheduled rides');
      
      final rides = await _bookingService.getScheduledRides();
      
      AppLogger.log('Repository: Found ${rides.length} scheduled rides');
      return Right(rides.cast<ScheduledRide>());
    } catch (e) {
      AppLogger.error('Repository: Failed to fetch scheduled rides', error: e);
      return Left('Failed to fetch scheduled rides: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, List<ScheduledRide>>> getScheduledRidesByStatus(ScheduledRideStatus status) async {
    try {
      AppLogger.log('Repository: Fetching rides by status: $status');
      
      final rides = await _bookingService.getScheduledRidesByStatus(status);
      
      AppLogger.log('Repository: Found ${rides.length} rides with status: $status');
      return Right(rides.cast<ScheduledRide>());
    } catch (e) {
      AppLogger.error('Repository: Failed to fetch rides by status', error: e);
      return Left('Failed to fetch rides by status: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, List<ScheduledRide>>> getUpcomingRides() async {
    try {
      AppLogger.log('Repository: Fetching upcoming rides');
      
      final rides = await _bookingService.getUpcomingRides();
      
      AppLogger.log('Repository: Found ${rides.length} upcoming rides');
      return Right(rides.cast<ScheduledRide>());
    } catch (e) {
      AppLogger.error('Repository: Failed to fetch upcoming rides', error: e);
      return Left('Failed to fetch upcoming rides: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, ScheduledRide>> updateScheduledRide(ScheduledRide ride) async {
    try {
      AppLogger.log('Repository: Updating scheduled ride: ${ride.id}');
      
      final rideModel = ScheduledRideModel.fromEntity(ride);
      final result = await _bookingService.updateScheduledRide(rideModel);
      
      AppLogger.log('Repository: Scheduled ride updated successfully');
      return Right(result);
    } catch (e) {
      AppLogger.error('Repository: Failed to update scheduled ride', error: e);
      return Left('Failed to update scheduled ride: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, void>> cancelScheduledRide(String rideId) async {
    try {
      AppLogger.log('Repository: Cancelling scheduled ride: $rideId');
      
      await _bookingService.cancelScheduledRide(rideId);
      
      AppLogger.log('Repository: Scheduled ride cancelled successfully');
      return const Right(null);
    } catch (e) {
      AppLogger.error('Repository: Failed to cancel scheduled ride', error: e);
      return Left('Failed to cancel scheduled ride: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, void>> deleteScheduledRide(String rideId) async {
    try {
      AppLogger.log('Repository: Deleting scheduled ride: $rideId');
      
      await _bookingService.deleteScheduledRide(rideId);
      
      AppLogger.log('Repository: Scheduled ride deleted successfully');
      return const Right(null);
    } catch (e) {
      AppLogger.error('Repository: Failed to delete scheduled ride', error: e);
      return Left('Failed to delete scheduled ride: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, List<ScheduledRide>>> getRidesNeedingNotification() async {
    try {
      AppLogger.log('Repository: Checking rides needing notification');
      
      final rides = await _bookingService.getRidesNeedingNotification();
      
      AppLogger.log('Repository: Found ${rides.length} rides needing notification');
      return Right(rides.cast<ScheduledRide>());
    } catch (e) {
      AppLogger.error('Repository: Failed to check rides needing notification', error: e);
      return Left('Failed to check rides needing notification: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, void>> markRideAsNotified(String rideId) async {
    try {
      AppLogger.log('Repository: Marking ride as notified: $rideId');
      
      // For now, we'll just log this action
      // In a real implementation, this would update the ride's notification status
      await Future.delayed(const Duration(milliseconds: 100));
      
      AppLogger.log('Repository: Ride marked as notified successfully');
      return const Right(null);
    } catch (e) {
      AppLogger.error('Repository: Failed to mark ride as notified', error: e);
      return Left('Failed to mark ride as notified: ${e.toString()}');
    }
  }
}
